<template>
  <div class="chat-container">
    <!-- 左侧部分  -->
    <div class="left-section">
      <!-- 左侧的左半部分 -->
      <div class="left-menu">
        <!-- 搜索框和下拉选择 -->
        <div class="search-section">
          <el-select v-model="filterRole" placeholder="请选择身份" class="filter-select" @change="changeType" clearable>
            <el-option v-if="isAdmin" label="全部" value=""></el-option>
            <el-option v-for="(roleItem, roleIndex) in roleList" :key="roleIndex" :label="roleItem.roleName" :value="roleItem.role"></el-option>
          </el-select>
          <el-input v-model.trim="queryUser.nickname" placeholder="请输入用户名称" prefix-icon="el-icon-search" class="search-input" @change="searchUser" clearable></el-input>
        </div>

        <!-- 用户列表 -->
        <div style="height: 90%" v-loading="isLoading">
          <div class="user-list infinite-list" v-if="filteredUsers.length > 0" v-infinite-scroll="loadMoreUser">
            <div
              v-for="(userItem, userIndex) in filteredUsers"
              :key="userIndex"
              :class="['user-item', selectedUser.id === userItem.id ? 'higth-class' : '']"
              @click="selectUser(userItem, userIndex)"
            >
              <div class="user-avatar">
                <el-avatar :size="40" :src="userItem.userAvatar">{{ userItem.nickname ? userItem.nickname.charAt(0) : '' }}</el-avatar>
              </div>
              <div class="user-info">
                <div class="user-name">{{ userItem.nickname }}</div>
              </div>
              <!-- <div class="club-tag">{{ userItem.role }}</div> -->
              <el-tag>{{ userItem.roleName }}</el-tag>
            </div>
          </div>
          <NoMore v-if="filteredUsers.length < 1" key="nomore1" text="暂无存档人员"></NoMore>
        </div>
      </div>

      <!-- 左侧的右半部分 -->
      <div class="chat-users" v-loading="chatUserListLoading">
        <div class="header">
          <div class="title">
            <div class="user-avatar" v-if="selectedUser && selectedUser.id">
              <el-avatar :size="40" :src="selectedUser.userAvatar" v-if="selectedUser.userAvatar"></el-avatar>
              <el-avatar :size="40" v-else>
                {{ selectedUser.id ? selectedUser.nickname.charAt(0) : '' }}
              </el-avatar>
            </div>
            <div class="user-info">
              <div class="user-name">{{ selectedUser.id ? selectedUser.nickname : '请选择用户' }}</div>
            </div>
          </div>

          <div class="tabs">
            <el-radio-group v-model="activeTab" size="small" @change="changeSingle">
              <el-radio-button label="single">单聊</el-radio-button>
              <el-radio-button label="group">群聊</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 用户列表 -->
        <div class="user-list" v-show="selectUserList.length > 0" v-infinite-scroll="loadMoreUserRight">
          <!-- 单聊 -->
          <div v-if="activeTab == 'single'" v-loading="chatUserListLoading">
            <div
              v-for="(selectUserItem, selectUserIndex) in selectUserList"
              :key="selectUserIndex"
              :class="['user-item', chatUserData.id === selectUserItem.id ? 'higth-class' : '']"
              @click="selectChatList(selectUserItem, 'single')"
            >
              <div class="user-avatar">
                <el-avatar :size="40" :src="selectUserItem.userAvatar">
                  {{ selectUserItem.nickname ? selectUserItem.nickname.charAt(0) : '' }}
                </el-avatar>
              </div>
              <div class="user-info">
                <div class="user-name">{{ selectUserItem.nickname }}</div>
              </div>
              <el-tag type="success">{{ selectUserItem.roleName }}</el-tag>
            </div>
          </div>
          <!-- 群聊 -->
          <div class="group" v-if="activeTab == 'group'" v-loading="chatUserListLoading">
            <div
              v-for="(selectUserItem, selectUserIndex) in selectUserList"
              :key="selectUserIndex"
              :class="['user-item', chatUserData.groupId === selectUserItem.groupId ? 'higth-class' : '']"
              @click="selectChatList(selectUserItem, 'group')"
            >
              <div class="user-avatar">
                <el-tag type="danger" style="position: absolute; top: 0; left: 0; transform: translateY(-50%)" v-if="selectUserItem.deleted == 1" size="mini">已解散</el-tag>
                <el-avatar :size="40" :src="selectUserItem.groupAvatar">
                  {{ selectUserItem.groupName ? selectUserItem.groupName.charAt(0) : '' }}
                </el-avatar>
              </div>
              <div class="user-info">
                <div class="user-name">{{ selectUserItem.groupName }}</div>
                <div class="club-tag">{{ selectUserItem.groupMember + '人' }}{{ '|' }}{{ '群主:' + selectUserItem.groupOwnerName }}</div>
              </div>
            </div>
          </div>
        </div>
        <NoMore v-show="selectUserList.length < 1" key="nomore2" text="暂无数据"></NoMore>
      </div>
    </div>

    <!-- 右侧部分 -->
    <div class="right-section" v-loading="chatLoading">
      <div class="chat-user-info">
        <div class="chat-user-name">{{ chatUserData.id || chatUserData.groupId ? chatUserData.nickname || chatUserData.groupName : '请选择用户或群聊' }}</div>
        <div style="margin-left: 10px">{{ '|' }}</div>
        <el-button type="text" class="refresh-btn" @click="reflsh">刷新</el-button>
      </div>
      <div class="message-type-tabs">
        <el-radio-group v-model="activeType" @change="changeTab">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="files">文件</el-radio-button>
        </el-radio-group>
      </div>
      <ChatHistory
        ref="chatHistory"
        :distance="1"
        :messageList="messageList"
        :userList="userArr"
        :activeType="activeType"
        :self="selectedUser"
        @loadMore="loadMoreChatList"
        @filterChange="filterChangeChatList"
      ></ChatHistory>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import ChatHistory from '@/components/ChatHistory/index.vue';
  import NoMore from '@/components/NoMore/index.vue';
  import {
    getRoleList,
    getUserList,
    getChatUserListSingle,
    getChatUserListGroup,
    getChatListSingle,
    getChatListGroup,
    getGroupUserList,
    getFileList
  } from '@/api/chatContent/chatFile';

  export default {
    name: 'chatFile',
    components: { ChatHistory, NoMore },
    data() {
      return {
        isAdmin: false, // 是否是管理员
        screenWidth: window.screen.width, // 屏幕宽度
        chooseType: '1', // 选择类型
        active: 1, // 当前激活项
        searchKeyword: '', // 搜索关键词
        activeTab: 'single', // 当前激活的标签页(single:单聊, group:群聊)
        activeType: 'all', // 当前激活的消息类型(all:全部, files:文件)
        filterRole: '', // 筛选的角色
        queryUser: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页数量
          nickname: '', // 用户昵称
          role: [] // 角色列表
        },
        isLoading1: false, // 加载状态1
        queryChatUser: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页数量
          tencentId: '' // 腾讯ID
        },
        chatUserTotal: 0, // 聊天用户总数
        chatUserLoading: false, // 聊天用户加载状态
        chatUserListLoading: false, // 聊天用户列表加载状态
        isLoading: false, // 加载状态
        userTotal: 0, // 用户总数
        filteredUsers: [], // 筛选后的用户列表
        selectUserList: [], // 已选择的用户列表
        roleList: [], // 角色列表
        messageList: [], // 消息列表
        allMessageList: [], // 消息列表
        messageTotal: 0, // 消息总数
        selectedUser: {}, // 当前选中的用户
        chatUserData: {}, // 聊天用户数据
        singleQuery: {
          sender: '',
          senderId: '', // 发送者ID
          receiverId: '', // 接收者ID
          content: '', // 消息内容
          times: '', // 时间
          pageNum: 1, // 页码
          pageSize: 10 // 每页数量
        },
        groupQuery: {
          sender: '', // 发送者ID
          groupId: '', // 群组ID
          content: '', // 消息内容
          times: '', // 时间
          pageNum: 1, // 页码
          pageSize: 10 // 每页数量
        },
        fileQuery: {
          sender: '',
          senderId: '',
          receiverId: '',
          groupId: '',
          fileName: '',
          fileType: '',
          times: '',
          pageNum: 1,
          pageSize: 10 // 每页数量
        },
        userArr: [], // 用户数组
        count: 10, // 计数
        chatLoading: false // 聊天加载状态
      };
    },
    created() {
      // 组件创建时获取角色列表
      this.getRoleListFn();
    },
    watch: {},
    computed: {
      ...mapGetters(['role', 'userInfo']) // 从vuex获取角色和用户信息
    },
    methods: {
      /**
       * 过滤聊天列表
       * @param {Object} data - 过滤条件
       */
      async filterChangeChatList(data) {
        console.log(data, '====================');
        this.messageList = [];
        if (this.activeTab == 'single') {
          this.singleQuery.content = data.content;
          this.singleQuery.sender = data.senderId;
          this.singleQuery.times = data.times;
          this.singleQuery.pageNum = 1;
          this.fileQuery.fileName = data.content;
          this.fileQuery.fileType = data.fileType;
          this.fileQuery.sender = data.senderId;
          this.fileQuery.times = data.times;
          this.fileQuery.pageNum = 1;
        } else {
          this.groupQuery.content = data.content;
          this.groupQuery.sender = data.senderId;
          this.groupQuery.times = data.times;
          this.groupQuery.pageNum = 1;
          this.fileQuery.fileName = data.content;
          this.fileQuery.fileType = data.fileType;
          this.fileQuery.sender = data.senderId;
          this.fileQuery.times = data.times;
          this.fileQuery.pageNum = 1;
        }
        // await this.initChatList(this.chatUserData, this.activeTab);
        if (this.activeType == 'all') {
          await this.initChatList(this.chatUserData, this.activeTab);
        } else {
          await this.initFileList(this.chatUserData, this.activeTab);
        }
      },

      /**
       * 切换消息类型标签页
       * @param {String} e - 消息类型(all/files)
       */
      changeTab(e) {
        if (e == 'all') {
          // 显示所有消息，使用深拷贝确保数据独立性
          this.reset();
          this.initChatList(this.chatUserData, this.activeTab);
        } else {
          // 只显示文件消息
          this.reset();
          this.initFileList(this.chatUserData, this.activeTab);
        }
      },

      /**
       * 加载更多聊天记录
       */
      async loadMoreChatList() {
        console.log('🚀🥶💩~ this.messageList.length', this.messageList.length);
        console.log('🚀🥶💩~ this.messageTotal', this.messageTotal);
        if (this.messageList.length == this.messageTotal) return;
        if (this.messageList.length < this.messageTotal) {
          if (this.activeTab == 'single') {
            this.singleQuery.pageNum++;
          } else {
            this.groupQuery.pageNum++;
          }
          console.log('🚀🥶💩~ this.singleQuery', this.singleQuery);
          console.log('🚀🥶💩~ this.groupQuery', this.groupQuery);
          // this.initChatList(this.chatUserData, this.activeTab);
          if (this.activeType == 'all') {
            await this.initChatList(this.chatUserData, this.activeTab);
          } else {
            this.fileQuery.pageNum++;
            await this.initFileList(this.chatUserData, this.activeTab);
          }
        }
      },

      /**
       * 获取用户列表
       */
      async getUserListFn() {
        this.isLoading = true;
        let { data } = await getUserList(this.queryUser);
        this.userTotal = +data.totalItems;
        this.filteredUsers = this.filteredUsers.concat(data.data);
        this.isLoading = false;
      },

      /**
       * 加载更多用户(左侧用户列表)
       */
      async loadMoreUser() {
        console.log('触底');
        let that = this;
        if (that.isLoading) return;
        that.isLoading = true;
        try {
          if (that.filteredUsers.length == that.userTotal) {
            // this.$message.warning('没有更多数据了');
            return;
          } else {
            that.queryUser.pageNum++;
            that.isLoading = false;
            await that.getUserListFn();
          }
        } catch (error) {
          console.log(error);
          that.isLoading = false;
        } finally {
          that.isLoading = false;
        }
      },

      /**
       * 加载更多用户(右侧聊天用户列表)
       */
      async loadMoreUserRight() {
        let that = this;
        if (that.chatUserListLoading) return;
        that.chatUserListLoading = true;
        try {
          if (that.chatUserTotal == that.selectUserList.length) {
            // this.$message.warning('没有更多数据了');
            return;
          } else {
            that.queryChatUser.pageNum++;
            await that.getChatUserList();
          }
        } catch (error) {
          console.log(error);
          that.chatUserListLoading = false;
        } finally {
          that.chatUserListLoading = false;
        }
      },

      /**
       * 获取聊天用户列表(单聊/群聊)
       */
      async getChatUserList() {
        let res;
        if (this.activeTab == 'single') {
          res = await getChatUserListSingle(this.queryChatUser);
          console.log(res);
        } else {
          res = await getChatUserListGroup(this.queryChatUser);
          console.log(res);
        }
        this.chatUserTotal = res.total;

        this.selectUserList = this.selectUserList.concat(res.data);
        console.log('🚀🥶💩~ this.selectUserList', this.selectUserList);
        this.chatUserListLoading = false;
      },

      /**
       * 刷新聊天记录
       */
      async reflsh() {
        console.log(this.singleQuery, this.groupQuery);
        if (this.activeTab == 'single') {
          if (!(this.singleQuery.senderId && this.singleQuery.receiverId)) return;
        } else {
          if (!this.groupQuery.groupId) return;
        }

        this.groupQuery.pageNum = 1;
        this.singleQuery.pageNum = 1;
        this.fileQuery.pageNum = 1;
        this.messageList = [];
        // this.initChatList(this.chatUserData, this.activeTab);
        if (this.activeType == 'all') {
          await this.initChatList(this.chatUserData, this.activeTab);
        } else {
          await this.initFileList(this.chatUserData, this.activeTab);
        }
      },

      /**
       * 重置查询条件
       */
      reset() {
        this.$refs.chatHistory.searchKey = '';
        this.$refs.chatHistory.filterDate = '';
        this.$refs.chatHistory.filterSender = '';
        this.singleQuery.content = '';
        this.singleQuery.times = '';
        this.singleQuery.sender = '';
        this.singleQuery.pageNum = 1;
        this.groupQuery.content = '';
        this.groupQuery.times = '';
        this.groupQuery.sender = '';
        this.groupQuery.pageNum = 1;
        this.fileQuery.pageNum = 1;
        this.fileQuery.fileName = '';
        this.fileQuery.times = '';
        this.fileQuery.sender = '';
        this.messageList = [];
        this.allMessageList = []; // 同时清空所有消息的副本
      },

      /**
       * 选择聊天记录
       * @param {Object} item - 选中的用户/群组
       * @param {String} type - 聊天类型(single/group)
       */
      async selectChatList(item, type) {
        this.chatUserData = { ...item };
        this.reset();
        if (type == 'single') {
          this.singleQuery.senderId = this.selectedUser.tencentId;
          this.singleQuery.receiverId = item.tencentId;
          this.fileQuery.senderId = this.selectedUser.tencentId;
          this.fileQuery.receiverId = item.tencentId;
          this.fileQuery.groupId = '';
          this.userArr = [this.selectedUser, this.chatUserData];
        } else {
          // this.chatUserData.groupId = '13766660055';
          this.groupQuery.groupId = this.chatUserData.groupId;
          this.fileQuery.senderId = '';
          this.fileQuery.receiverId = '';
          this.fileQuery.groupId = this.chatUserData.groupId;
          let { data } = await getGroupUserList({
            groupId: this.chatUserData.groupId,
            pageNum: 1,
            pageSize: 100
          });
          this.userArr = data;
        }
        if (this.activeType == 'all') {
          await this.initChatList(item, type);
        } else {
          console.log('🚀🥶💩~ type', type);
          await this.initFileList(item, type);
        }
      },

      /**
       * 初始化聊天列表
       * @param {Object} item - 用户/群组信息
       * @param {String} type - 聊天类型(single/group)
       */
      async initChatList(item, type) {
        let res;
        this.chatLoading = true;
        if (type == 'single') {
          res = await getChatListSingle(this.singleQuery);
        } else {
          res = await getChatListGroup(this.groupQuery);
        }
        this.messageTotal = +res.data.totalItems;
        let arr = this.messageList.concat(res.data.data);
        this.messageList = JSON.parse(JSON.stringify(arr));
        this.messageList.forEach((item) => {
          if (item.messageType == 'TIMRelayElem' && item.messageContent) {
            let obj;
            obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
            item.messageContent = obj;
          }
        });
        // 保存所有消息的副本用于筛选，确保使用深拷贝
        this.allMessageList = JSON.parse(JSON.stringify(this.messageList));
        this.chatLoading = false;
      },
      /**
       * 初始化文件列表
       * @param {Object} item - 用户/群组信息
       * @param {String} type - 聊天类型(single/group)
       */
      async initFileList(item, type) {
        console.log('🚀🥶💩~ type', type);
        let res;
        this.chatLoading = true;
        console.log('|||||||||||🚀🥶💩~ this.fileQuery', this.fileQuery);
        this.fileQuery.chatType = type === 'single' ? '1' : '2';
        res = await getFileList(this.fileQuery);

        this.messageTotal = +res.data.totalItems;
        let arr = this.messageList.concat(res.data.data);
        this.messageList = JSON.parse(JSON.stringify(arr));
        this.messageList.forEach((item) => {
          if (item.messageType == 'TIMRelayElem') {
            let obj;
            obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
            item.messageContent = obj;
          }
        });
        // 保存所有消息的副本用于筛选，确保使用深拷贝
        this.allMessageList = JSON.parse(JSON.stringify(this.messageList));
        this.chatLoading = false;
      },

      /**
       * 切换单聊/群聊
       * @param {String} e - 聊天类型(single/group)
       */
      changeSingle(e) {
        this.chatUserListLoading = true;

        if (this.selectedUser.id) {
          this.queryChatUser.pageNum = 1;
          this.reset();
          this.chatUserData = {};
          this.selectUserList = [];
          this.messageList = [];
          this.getChatUserList();
          // 防抖
        } else {
          this.chatUserListLoading = false;

          return;
        }
      },

      /**
       * 获取角色列表
       */
      async getRoleListFn() {
        let { data } = await getRoleList();
        console.log(data);
        this.roleList = data.data;
      },

      /**
       * 搜索用户
       * @param {String} e - 搜索关键词
       */
      searchUser(e) {
        this.selectUserList = [];
        this.selectedUser = {};
        this.filteredUsers = [];
        this.chatUserData = {};
        this.reset();
        if (this.queryUser.role.length > 0) {
          this.queryUser.nickname = e;
          this.queryUser.pageNum = 1;
          this.getUserListFn();
        } else {
          return this.$message.warning('请先选择身份');
        }
      },

      /**
       * 更换身份
       * @param {String} e - 角色值
       */
      changeType(e) {
        if (!!e) {
          this.selectUserList = [];
          this.selectedUser = {};
          this.filteredUsers = [];
          let arr = [];
          if (this.isAdmin) {
            arr = this.roleList.map((i) => i.role);
          } else {
            arr = [e];
          }
          this.queryUser.role = arr;
          this.queryUser.pageNum = 1;
          this.getUserListFn();
        } else {
          this.filteredUsers = [];
        }
      },

      /**
       * 选择用户
       * @param {Object} user - 用户信息
       * @param {Number} index - 用户索引
       */
      async selectUser(user, index) {
        if (this.selectedUser && this.selectedUser.id == user.id) return;
        this.selectedUser = { ...user };

        this.queryChatUser.tencentId = user.tencentId;
        this.queryChatUser.pageNum = 1;
        this.singleQuery = {
          sender: '',
          senderId: '', // 发送者ID
          receiverId: '', // 接收者ID
          content: '', // 消息内容
          times: '', // 时间
          pageNum: 1, // 页码
          pageSize: 10 // 每页数量
        };
        this.groupQuery = {
          senderId: '',
          groupId: '',
          content: '',
          times: '',
          pageNum: 1,
          pageSize: 10
        };
        this.selectUserList = [];
        this.chatUserData = {};
        this.reset();
        await this.getChatUserList();
      },

      /**
       * 防抖函数
       * @param {Function} fn - 要执行的函数
       * @param {Number} wait - 等待时间
       * @returns {Function} - 防抖后的函数
       */
      debounce(fn, wait) {
        var timeout = null;
        return function () {
          if (timeout !== null) clearTimeout(timeout);
          timeout = setTimeout(fn, wait);
        };
      }
    }
  };
</script>

<style lang="scss" scoped>
  .chat-container {
    display: flex;
    height: calc(100vh - 84px);
    background-color: #f0f2f5;
    padding: 16px;
    gap: 16px;
  }

  .left-section {
    width: 50%;
    display: flex;
    gap: 16px;
    height: 100%;

    .left-menu {
      width: 50%;
      height: 100%;
      background: #fff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .chat-users {
      width: 50%;
      background: #fff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  .right-section {
    width: 50%;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .chat-user-info {
    padding: 16px 24px 0 24px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    // border-bottom: 1px solid #ebeef5;

    .chat-user-name {
      font-size: 15px;
      color: #303133;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #67c23a;
        margin-right: 2px;
      }
    }

    .refresh-btn {
      padding: 6px 12px;
      font-size: 13px;
      // color: #606266;
      transition: all 0.3s;
      border-radius: 4px;

      // &:hover {
      //   color: #409eff;
      //   background-color: #ecf5ff;
      // }

      i {
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }

  .chat-header {
    padding: 12px 24px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: center;

    .el-radio-group {
      width: 240px;

      :deep(.el-radio-button__inner) {
        padding: 8px 0;
        font-size: 13px;
        border-color: #dcdfe6;

        &:hover {
          color: #409eff;
        }
      }

      :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
        background-color: #409eff;
        border-color: #409eff;
        box-shadow: -1px 0 0 0 #409eff;
      }
    }
  }

  .header {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .user-avatar {
        margin-right: 12px;
        position: relative;

        :deep(.el-avatar) {
          border-radius: 6px;
          background-color: #409eff;
          color: #fff;
          font-weight: 500;
        }

        :deep(.el-tag) {
          position: absolute;
          top: 0;
          left: 0;
          transform: translateY(-50%);
          z-index: 1;
          border-radius: 2px;
          padding: 0 4px;
          height: 18px;
          line-height: 16px;
          font-size: 12px;
        }
      }

      .user-info {
        flex: 1;
        min-width: 0;

        .user-name {
          font-size: 15px;
          color: #303133;
          font-weight: 500;
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .tabs {
      display: flex;
      justify-content: center;

      .el-radio-group {
        width: 240px;

        :deep(.el-radio-button__inner) {
          padding: 8px 0;
          font-size: 13px;
          border-color: #dcdfe6;

          &:hover {
            color: #409eff;
          }
        }

        :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
          background-color: #409eff;
          border-color: #409eff;
          box-shadow: -1px 0 0 0 #409eff;
        }
      }
    }
  }

  .search-section {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    // flex-direction: column;
    gap: 12px;
    height: 10%;

    .filter-select {
      width: 50%;

      :deep(.el-input__inner) {
        border-radius: 4px;
      }
    }

    .search-input {
      width: 100%;

      :deep(.el-input__inner) {
        border-radius: 4px;

        &:focus {
          border-color: #409eff;
        }
      }

      :deep(.el-input__prefix) {
        height: 32px !important;
        left: 12px;
        color: #909399;
      }

      :deep(.el-input__inner) {
        padding-left: 36px;
      }
    }
  }

  .user-list {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    padding: 8px 0;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .higth-class {
      background: #f5f7fa;
    }

    .user-item {
      display: flex;
      align-items: center;
      padding: 12px 24px;
      cursor: pointer;
      transition: background-color 0.3s;
      .higth-class {
        background: #f5f7fa;
      }
      &:hover {
        background-color: #f5f7fa;
      }

      .user-avatar {
        margin-right: 12px;
        position: relative;

        :deep(.el-avatar) {
          border-radius: 6px;
          background-color: #409eff;
          color: #fff;
          font-weight: 500;
        }

        :deep(.el-tag) {
          position: absolute;
          top: 0;
          left: 0;
          transform: translateY(-50%);
          z-index: 1;
          border-radius: 2px;
          padding: 0 4px;
          height: 18px;
          line-height: 16px;
          font-size: 12px;
        }
      }

      .user-info {
        flex: 1;
        min-width: 0;

        .user-name {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .club-tag {
        display: inline-block;
        padding: 2px 8px;
        background-color: #f0f2f5;
        color: #909399;
        border-radius: 10px;
        font-size: 11px;
      }

      .user-action {
        margin-left: 12px;

        .el-tag {
          border-radius: 10px;
          padding: 0 8px;
          height: 20px;
          line-height: 18px;
          border: 1px solid #e4e7ed;
          background-color: #f5f7fa;
          color: #909399;
        }
      }
    }
  }

  .chat-history {
    // flex: 1;
    height: calc(100vh - 284px);
    overflow-y: auto;
  }

  .message-type-tabs {
    padding: 6px 24px;
    background: #fff;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: center;

    .el-radio-group {
      width: 240px;

      :deep(.el-radio-button__inner) {
        padding: 8px 0;
        font-size: 13px;
        border-color: #dcdfe6;

        &:hover {
          color: #409eff;
        }
      }

      :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
        background-color: #409eff;
        border-color: #409eff;
        box-shadow: -1px 0 0 0 #409eff;
      }
    }
  }
</style>

<style>
  .el-radio-button {
    width: 50%;
  }

  .el-radio-button__inner {
    width: 100%;
  }

  .el-select {
    width: 100%;
  }

  .el-input__inner {
    border-radius: 4px;
  }
</style>
