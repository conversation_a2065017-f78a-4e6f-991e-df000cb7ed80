<template>
  <div class="app-container">
    <!-- Tab 切换 -->
    <div class="tab-container">
      <el-tabs v-model="activeTab" style="width: 10%" @tab-click="handleTabClick">
        <el-tab-pane label="角色" name="role" />
        <el-tab-pane label="角色建群关系" name="groupRelation" />
      </el-tabs>
      <template>
        <el-popover placement="right-end" trigger="hover" width="500">
          <i slot="reference" class="el-icon-info" style="cursor: pointer; margin-left: 20px" />
          <template #default>
            <div v-html="busininessDesc"></div>
          </template>
        </el-popover>
      </template>
    </div>

    <!-- 搜索栏 -->
    <el-form ref="searchForm" :model="searchForm" :rules="searchRules" class="rule-form">
      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="角色名称" prop="roleName">
            <el-input v-model="searchForm.roleName" clearable placeholder="请输入角色名称" @keyup.enter.native="handleSearch" />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="关联应用" prop="roleApp">
            <el-select v-model="searchForm.roleApp" clearable filterable placeholder="请选择关联应用">
              <el-option v-for="item in appOptions" :key="item.appValue" :label="item.appName" :value="item.appValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <div class="search-actions">
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <!-- 表格区域 -->
    <div v-loading="loading">
      <div v-if="activeTab === 'role'">
        <el-table v-if="paginatedRoleList.length > 0" :data="paginatedRoleList" :header-cell-style="getRowClass" style="width: 100%">
          <el-table-column align="center" label="序号" type="index" width="80" />
          <el-table-column label="关联角色名称" prop="roleName">
            <template v-slot="{ row }">
              <el-tooltip :content="row.roleName" :disabled="row.roleName.length <= 10" placement="top">
                <span type="text">{{ row.roleName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="关联应用" prop="roleApp">
            <template v-slot="{ row }">
              <el-tooltip :content="row.roleApp" :disabled="row.roleApp.length <= 10" placement="top">
                <span>{{ row.roleApp }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="新增时间" prop="createTime" />
          <el-table-column label="状态" prop="isDeleted">
            <template #default="{ row }">
              <el-tag :type="row.isDeleted === '1' ? 'danger' : 'primary'">{{ row.isDeleted === '1' ? '禁用' : '启用' }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div v-else class="nomore">
          <el-image src="https://document.dxznjy.com/automation/1728442200000" style="width: 100px; height: 100px" />
          <div style="color: #999; margin-top: 20px">暂无数据</div>
        </div>
      </div>
      <div v-if="activeTab === 'groupRelation'">
        <el-table v-if="groupRelationList.length > 0" :data="groupRelationList" :header-cell-style="getRowClass" style="width: 100%">
          <el-table-column align="center" label="序号" type="index" width="80" />
          <el-table-column label="自动建群角色组" prop="roleName">
            <template v-slot="{ row }">
              <el-tooltip :content="row.roles" :disabled="row.roles.length <= 10" placement="top">
                <span class="ellipsis">{{ row.roles }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="关联应用" prop="roleApp">
            <template v-slot="{ row }">
              <el-tooltip :content="row.app" :disabled="row.app.length <= 10" placement="top">
                <span class="ellipsis">{{ row.app }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="新增时间" prop="createTime" />
        </el-table>
        <div v-else class="nomore">
          <el-image src="https://document.dxznjy.com/automation/1728442200000" style="width: 100px; height: 100px" />
          <div style="color: #999; margin-top: 20px">暂无数据</div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <el-row align="left" justify="left" style="height: 60px" type="flex">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>
  </div>
</template>

<script>
  import roleApi from '@/api/system/role';

  export default {
    name: 'BusinessRole',
    props: {
      visible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        activeTab: 'role',
        formData: {
          roleList: [],
          groupRelationList: []
        },
        searchForm: {
          roleName: '',
          roleApp: '',
          app: '',
          pageNum: 1,
          pageSize: 10
        },
        appOptions: [],
        searchRules: {
          roleName: [
            {
              validator: (_rule, value, callback) => (value && value.length > 50 ? callback(new Error('角色名称不得超过50个字符')) : callback()),
              trigger: 'blur'
            }
          ]
        },
        loading: false,
        busininessDesc:
          '<div class="rich-text-content">\n' +
          '  <p >\n' +
          '    角色和建群关系列表数据均由关联系统提供，\n' +
          '    <span >IM后台仅支持查看相关信息。</span>\n' +
          '  </p>\n' +
          // '  <p >\n' +
          // '    <span >2、</span>\n' +
          // '    为避免人员误操作导致系统问题，\n' +
          // '    <span >状态禁用后不会影响已存在的好友关系组</span>，\n' +
          // '    <span >好友关系组需要手动删除</span>;\n' +
          // '  </p>\n' +
          '</div>',
        // busininessDesc:
        //   '1、角色和建群关系列表数据均由关联系统提供，IM后台仅支持查看相关信息。\n' +
        //   ' 2、为避免人员误操作导致系统问题，状态禁用后不会影响已存在的好友关系组，好友关系组需要手动删除;',
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        }
      };
    },
    computed: {
      drawerVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      },
      paginatedRoleList() {
        return this.formData?.roleList; // 后端分页，直接使用返回数据
      },
      groupRelationList() {
        return this.formData?.groupRelationList; // 后端分页，直接使用返回数据
      }
    },
    watch: {
      visible(newVal) {
        if (newVal) this.loadData();
        else this.resetForm();
      }
    },
    created() {
      this.loadAppData();
      this.loadData();
    },
    methods: {
      async loadAppData() {
        try {
          const res = await roleApi.getAppList();
          this.appOptions = res.data || [];
        } catch (error) {
          console.error('加载应用选项失败:', error);
        }
      },

      getRowClass({ rowIndex }) {
        return rowIndex === 0 ? 'background:#f5f7fa' : '';
      },
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.pagination.currentPage = 1;
        this.searchForm.pageSize = val;
        this.searchForm.pageNum = 1;
        this.loadData();
      },
      handleCurrentChange(val) {
        this.pagination.currentPage = val;
        this.searchForm.pageNum = val;
        this.loadData();
      },
      handleTabClick() {
        this.pagination.currentPage = 1;
        if (this.activeTab === 'role') {
          this.busininessDesc =
            '<div class="rich-text-content">\n' +
            '  <p >\n' +
            '    角色和建群关系列表数据均由关联系统提供，\n' +
            '    <span >IM后台仅支持查看相关信息。</span>\n' +
            '  </p>\n' +
            // '  <p >\n' +
            // '    <span >2、</span>\n' +
            // '    为避免人员误操作导致系统问题，\n' +
            // '    <span >状态禁用后不会影响已存在的好友关系组</span>，\n' +
            // '    <span >好友关系组需要手动删除</span>;\n' +
            // '  </p>\n' +
            '</div>';
        } else {
          // this.busininessDesc = '角色和建群关系列表数据均由关联系统提供，IM后台仅支持查看相关信息。';
          this.busininessDesc =
            '<div class="rich-text-content">\n' +
            '  <p >\n' +
            '    <span>1、</span>\n' +
            '    角色和建群关系列表数据均由关联系统提供，IM后台仅支持查看相关信息。\n' +
            '    <span >IM后台仅支持查看相关信息。</span>\n' +
            '  </p>\n' +
            '</div>';
        }
        this.resetForm();
        this.loadData();
      },
      async loadData() {
        this.loading = true;
        try {
          const params = {
            roleName: this.searchForm.roleName || '',
            pageNum: this.pagination.currentPage,
            pageSize: this.pagination.pageSize
          };
          if (this.activeTab === 'role') {
            params.roleApp = this.searchForm.roleApp || '';
            const { data } = await roleApi.getRoles(params);
            this.formData.roleList = data.data || [];
            this.pagination.total = parseInt(data.totalCount) || 0;
          } else {
            params.app = this.searchForm.roleApp || '';
            const { data } = await roleApi.groupRole(params);
            this.formData.groupRelationList = data.data || [];
            this.pagination.total = parseInt(data.totalCount) || 0;
          }
        } catch (error) {
          this.$message.error('数据加载失败，请稍后重试');
          console.error('加载数据失败:', error);
        } finally {
          this.loading = false;
        }
      },
      handleSearch() {
        this.$refs.searchForm.validate((valid) => {
          if (valid) {
            this.pagination.currentPage = 1;
            this.searchForm.pageNum = 1;
            this.loadData();
          }
        });
      },
      handleReset() {
        this.$refs.searchForm.resetFields();
        this.pagination.currentPage = 1;
        this.pagination.pageSize = 10;
        this.searchForm.pageNum = 1;
        this.searchForm.pageSize = 10;
        this.loadData();
      },
      resetForm() {
        this.formData.roleList = [];
        this.formData.groupRelationList = [];
        this.searchForm.roleName = '';
        this.searchForm.roleApp = '';
        this.$refs.searchForm?.resetFields();
        this.pagination.currentPage = 1;
        this.pagination.total = 0;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .tab-container {
    background-color: #fff;
    display: flex;
    align-items: center;
  }

  .rule-form {
    border-radius: 8px;

    .search-actions {
      display: flex;
      gap: 10px;
    }
  }

  .nomore {
    padding: 100px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0.8;
  }

  .table-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    min-height: 300px;
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-input,
  .el-select {
    width: 200px;
  }

  .el-table {
    margin-bottom: 20px;
  }

  .el-table th,
  .el-table td {
    padding: 8px 0;
  }

  .ellipsis {
    display: inline-block;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
