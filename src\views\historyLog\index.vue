<template>
  <div class="app-container">
    <!-- 搜索表单区域 -->
    <el-form ref="searchForm" label-width="90px" :model="searchForm">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="操作时间：" prop="timeRange">
            <el-date-picker v-model="searchForm.timeRange" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" size="small" />
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24">
          <el-form-item label="用户：" prop="user">
            <el-input v-model.trim="searchForm.user" size="small" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24">
          <el-form-item label="登录账号：" prop="mobile">
            <el-input v-model.trim="searchForm.mobile" size="small" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24">
          <el-form-item label="模块：" prop="module">
            <el-input v-model.trim="searchForm.module" size="small" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="4" style="padding-left: 20px">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh-left" @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div style="margin-bottom: 30px" />

    <!-- 表格区域 -->
    <div v-loading="loading">
      <div v-show="!currentPageData.length" class="nomore">
        <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000" />
        <div style="color: #999; margin-top: 20px">暂无数据</div>
      </div>
      <el-table v-show="currentPageData.length" :data="currentPageData" style="width: 100%" :header-cell-style="getRowClass" fit size="large">
        <el-table-column label="序号" type="index" width="80" isSubmitLoading />
        <el-table-column prop="createTime" label="操作时间" width="200" header-isSubmitLoading />
        <el-table-column prop="user" label="用户" width="260">
          <template v-slot="scope">
            <div>
              <el-tag style="margin-right: 4px">{{ scope.row.user ? scope.row.user : '-' }}</el-tag>
              <span>{{ scope.row.mobile }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="module" label="模块">
          <template v-slot="scope">
            <span>{{ scope.row.module ? scope.row.module : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="operationType" label="操作类型" width="120" header-isSubmitLoading />
        <el-table-column prop="operationalBehavior" label="操作行为" header-isSubmitLoading />
        <!--        <el-table-column type="index" label="序号" width="100" header-align="center" align="center" />-->
        <!--        <el-table-column label="规则名称" header-align="left">-->
        <!--          <template v-slot="scope">-->
        <!--            <el-tooltip :content="scope.row.ruleName" placement="top">-->
        <!--              <span @click="editRule(scope.row, false)" class="rule-name">-->
        <!--                {{ scope.row.ruleName }}-->
        <!--              </span>-->
        <!--            </el-tooltip>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column prop="ruleObjectValue" label="监控对象" header-align="left">-->
        <!--          <template v-slot="scope">-->
        <!--            <span v-if="scope.row.ruleObjectValue">{{ scope.row.ruleObjectValue }}</span>-->
        <!--            <span v-else>-</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column prop="sensitiveWordName" label="监控词表" header-align="left" width="300">-->
        <!--          <template v-slot="scope">-->
        <!--            <el-tooltip class="item" effect="dark" :content="scope.row.sensitiveWordName" placement="top" v-if="scope.row.sensitiveWordName">-->
        <!--              <span class="ellipsis-text">{{ scope.row.sensitiveWordName }}</span>-->
        <!--            </el-tooltip>-->
        <!--            <span v-else>-</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column prop="numberOfHits" label="命中数" header-align="left" />-->
        <!--        <el-table-column prop="createTime" label="新增时间" header-align="left" />-->
        <!--        <el-table-column label="操作" header-align="left" width="150">-->
        <!--          <template v-slot="scope">-->
        <!--            <el-button type="text" @click.stop="editRule(scope.row, true)">编辑</el-button>-->
        <!--            <el-button type="text" style="color: #ff4d4f" @click.stop="deleteRule(scope.row.id)">删除</el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>
    </div>

    <div style="margin-bottom: 30px" />

    <!-- 分页器 -->
    <el-pagination
      :current-page="pagination.currentPage"
      :page-sizes="[10, 20, 30, 40, 50]"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.totalItems"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
  import logApi from '@/api/historyLog';

  export default {
    name: 'OperationLog',
    data() {
      return {
        tableLoading: false,
        searchForm: {
          timeRange: [],
          user: undefined,
          mobile: undefined,
          module: undefined,
          startTime: undefined,
          endTime: undefined,
          pageNum: 1,
          pageSize: 10
        },
        allData: [], // 存储所有数据（可选，视需求使用）
        logData: [], // 当前显示的数据
        loading: false,
        pagination: {
          currentPage: 1,
          pageSize: 10,
          totalItems: 0
        }
      };
    },

    computed: {
      // 计算当前页的数据
      currentPageData() {
        return this.logData; // 直接使用 logData，假设后端已分页
      }
    },

    created() {
      // 初始化时加载数据
      this.loadData();
    },

    methods: {
      // 动态表头样式
      getRowClass({ rowIndex }) {
        if (rowIndex === 0) {
          return 'background:#f5f7fa';
        }
      },

      // 格式化日期为本地时间
      formatToLocalDateTime(date) {
        const pad = (num) => String(num).padStart(2, '0');
        return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}T${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
      },

      // 加载当前页数据
      async loadData() {
        this.loading = true;
        this.tableLoading = true;

        try {
          // 创建请求参数对象
          const params = {
            pageNum: this.pagination.currentPage,
            pageSize: this.pagination.pageSize,
            user: this.searchForm.user,
            mobile: this.searchForm.mobile,
            module: this.searchForm.module
          };

          // 处理时间范围
          if (this.searchForm.timeRange && this.searchForm.timeRange.length > 0) {
            params.startTime = this.formatToLocalDateTime(this.searchForm.timeRange[0]);
            params.endTime = this.formatToLocalDateTime(this.searchForm.timeRange[1]);
          }

          // 调用接口
          const res = await logApi.logList(params);
          this.logData = res.data.data || [];
          this.pagination.totalItems = parseInt(res.data.totalItems) || 0;
        } catch (error) {
          console.error('Failed to load data:', error);
          this.$message.error('数据加载失败，请稍后重试');
        } finally {
          this.loading = false;
          this.tableLoading = false;
        }
      },

      // 查询
      handleSearch() {
        this.pagination.currentPage = 1; // 重置到第一页
        this.searchForm.pageNum = 1;
        this.loadData();
      },

      // 重置
      handleReset() {
        this.$refs.searchForm.resetFields();
        this.pagination.currentPage = 1; // 重置到第一页
        this.pagination.pageSize = 10;
        this.searchForm.pageNum = 1;
        this.searchForm.pageSize = 10;
        this.loadData();
      },

      // 每页条数变化
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.searchForm.pageSize = val;
        this.pagination.currentPage = 1; // 重置到第一页
        this.searchForm.pageNum = 1;
        this.loadData();
      },

      // 当前页变化
      handleCurrentChange(val) {
        this.pagination.currentPage = val;
        this.searchForm.pageNum = val;
        this.loadData();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .nomore {
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .el-table {
    border: 1px solid #ebeef5;
  }

  .el-table th,
  .el-table td {
    border-right: none;
  }

  .pagination-container {
    padding: 20px 0;
    background-color: #fff;
  }

  .rule-name {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    color: #409eff;
  }

  .rule-name:hover {
    text-decoration: underline;
  }

  .ellipsis-text {
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
</style>
