module.exports = {
  types: [
    { value: 'feat', name: 'feat:     新功能' },
    { value: 'fix', name: 'fix:      修复Bug' },
    { value: 'docs', name: 'docs:     文档变更' },
    { value: 'style', name: 'style:    代码格式' },
    { value: 'refactor', name: 'refactor: 重构代码' },
    { value: 'perf', name: 'perf:     性能优化' },
    { value: 'test', name: 'test:     添加测试' },
    { value: 'chore', name: 'chore:    构建过程或辅助工具的变更' },
    { value: 'ci', name: 'ci:       CI 配置变更' },
  ],
  scopes: [
    { name: 'login' },
    { name: 'user' },
    { name: 'dashboard' },
    { name: 'order' },
    { name: 'product' },
    { name: 'setting' },
  ],
  messages: {
    type: '选择你要提交的类型:',
    scope: '选择一个模块 (可选):',
    customScope: '请输入自定义模块名:',
    subject: '写一个简短的描述:\n',
    body: '详细描述（可选）。使用 "|" 换行:\n',
    footer: '关联的问题号（可选）: 如 #31, #34:\n',
    confirmCommit: '确认提交？',
  },
  allowCustomScopes: true,
  allowBreakingChanges: ['feat', 'fix'],
};
