<template>
  <div class="chat-container">
    <!-- 左侧部分  -->
    <div class="left-section">
      <!-- 左侧的右半部分 -->
      <div v-loading="chatUserListLoading" class="chat-users">
        <div class="header">
          <div class="title">
            <div v-if="selectedUser && selectedUser.id" class="user-avatar">
              <el-avatar v-if="selectedUser.userAvatar" :size="40" :src="selectedUser.userAvatar"></el-avatar>
              <el-avatar v-else :size="40">
                {{ selectedUser.id ? selectedUser.nickname.charAt(0) : '' }}
              </el-avatar>
            </div>
            <div class="user-info">
              <div class="user-name">{{ selectedUser.id ? selectedUser.nickname : '请选择群组' }}</div>
            </div>
          </div>
          <div class="search-box">
            <el-input v-model="searchKeyword" clearable placeholder="请输入群组名称" @clear="clearSearch" @keyup.enter.native="toSearchGroup" />
          </div>
        </div>

        <!-- 群组列表 -->
        <div v-show="groupList.length > 0" v-infinite-scroll="loadMoreGroup" class="user-list">
          <!-- 群聊 -->
          <div v-loading="chatUserListLoading" class="group">
            <div
              v-for="(selectUserItem, selectUserIndex) in groupList"
              :key="selectUserIndex"
              :class="['user-item', chatUserData.groupId === selectUserItem.groupId ? 'higth-class' : '']"
              @click="selectChatList(selectUserItem, 'group')"
            >
              <div class="user-avatar">
                <el-tag v-if="selectUserItem.deleted == 1" size="mini" style="position: absolute; top: 0; left: 0; transform: translateY(-50%)" type="danger">已解散</el-tag>
                <el-avatar :size="40" :src="selectUserItem.groupAvatar">
                  {{ selectUserItem.groupName ? selectUserItem.groupName.charAt(0) : '' }}
                </el-avatar>
              </div>
              <div class="user-info">
                <div class="user-name">{{ selectUserItem.groupName }}</div>
                <div class="club-tag">{{ selectUserItem.groupMemberCount + '人' }}{{ '|' }}{{ '群主:' + selectUserItem.groupOwnerName }}</div>
              </div>
            </div>
          </div>
        </div>
        <NoMore v-show="groupList.length < 1" key="nomore2" text="暂无数据"></NoMore>
      </div>
    </div>

    <!-- 右侧部分 -->
    <div v-loading="chatLoading" class="right-section">
      <div class="chat-user-info">
        <div class="chat-user-name">{{ chatUserData.id || chatUserData.groupId ? chatUserData.nickname || chatUserData.groupName : '请选择用户或群聊' }}</div>
        <div style="margin-left: 10px">{{ '|' }}</div>
        <el-button class="refresh-btn" type="text" @click="refresh">刷新</el-button>
      </div>
      <div class="message-type-tabs">
        <el-radio-group v-model="activeType" @change="changeTab">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="files">文件</el-radio-button>
        </el-radio-group>
      </div>
      <ChatHistory
        ref="chatHistory"
        :activeType="activeType"
        :distance="1"
        :messageList="messageList"
        :self="selectedUser"
        :userList="userArr"
        @filterChange="filterChangeChatList"
        @loadMore="loadMoreChatList"
      ></ChatHistory>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import ChatHistory from '@/components/ChatHistory/index.vue';
  import NoMore from '@/components/NoMore/index.vue';
  import { getRoleList, getUserList, getChatUserListGroup, getChatListGroup, getGroupUserList, getFileList, getGroupListApi } from '@/api/chatContent/chatFile';

  export default {
    name: 'chatFile',
    components: { ChatHistory, NoMore },
    data() {
      return {
        isAdmin: false, // 是否是管理员
        screenWidth: window.screen.width, // 屏幕宽度
        chooseType: '1', // 选择类型
        active: 1, // 当前激活项
        searchKeyword: '', // 搜索关键词
        activeTab: 'group', // 当前激活的标签页(single:单聊, group:群聊)
        activeType: 'all', // 当前激活的消息类型(all:全部, files:文件)
        filterRole: '', // 筛选的角色
        queryUser: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页数量
          nickname: '', // 用户昵称
          role: [] // 角色列表
        },
        isLoading1: false, // 加载状态1
        queryChatUser: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页数量
          tencentId: '' // 腾讯ID
        },
        chatUserTotal: 0, // 聊天用户总数
        chatUserLoading: false, // 聊天用户加载状态
        chatUserListLoading: false, // 聊天用户列表加载状态
        isLoading: false, // 加载状态
        userTotal: 0, // 用户总数
        filteredUsers: [], // 筛选后的用户列表
        selectUserList: [], // 已选择的用户列表
        roleList: [], // 角色列表
        messageList: [], // 消息列表
        allMessageList: [], // 消息列表
        messageTotal: 0, // 消息总数
        selectedUser: {}, // 当前选中的用户
        chatUserData: {}, // 聊天用户数据
        groupQuery: {
          sender: '', // 发送者ID
          groupId: '', // 群组ID
          content: '', // 消息内容
          times: '', // 时间
          pageNum: 1, // 页码
          pageSize: 10 // 每页数量,
        },
        fileQuery: {
          sender: '',
          senderId: '',
          receiverId: '',
          groupId: '',
          fileName: '',
          fileType: '',
          times: '',
          pageNum: 1,
          pageSize: 10 // 每页数量
        },
        userArr: [], // 用户数组
        count: 10, // 计数
        chatLoading: false, // 聊天加载状态
        searchGroup: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页数量,
          groupName: ''
        },
        groupList: [] //群聊列表
      };
    },
    created() {
      // 组件创建时获取角色列表
      this.getRoleListFn();
      this.getGroupData();
    },
    watch: {},
    computed: {
      ...mapGetters(['role', 'userInfo']) // 从vuex获取角色和用户信息
    },
    methods: {
      /**
       * 清空搜索框
       */
      clearSearch() {
        this.searchKeyword = '';
        this.toSearchGroup();
      },

      /**
       * 搜索群聊名称
       */
      async toSearchGroup() {
        console.log('🚀🥶💩~ this.searchKeyword', this.searchKeyword);
        this.searchGroup.groupName = this.searchKeyword;
        this.searchGroup.pageNum = 1;
        const { code, data } = await getGroupListApi(this.searchGroup);
        if (code === 20000) {
          this.groupList = data.data;
        }
      },

      blurSearch() {
        this.searchGroup.pageNum = 1;
        this.searchGroup.groupName = this.searchKeyword;
        this.getGroupData();
      },

      /**
       * 获取群聊列表
       * @returns {Promise<void>}
       */
      async getGroupData() {
        const { code, data } = await getGroupListApi(this.searchGroup);
        if (code === 20000) {
          this.groupList = this.groupList.concat(data.data);
        }
      },

      /**
       * 过滤聊天列表
       * @param {Object} data - 过滤条件
       */
      async filterChangeChatList(data) {
        console.log(data, '====================');
        this.messageList = [];
        this.groupQuery.content = data.content;
        this.groupQuery.sender = data.senderId;
        this.groupQuery.times = data.times;
        this.groupQuery.pageNum = 1;
        this.fileQuery.fileName = data.content;
        this.fileQuery.fileType = data.fileType;
        this.fileQuery.sender = data.senderId;
        this.fileQuery.times = data.times;
        this.fileQuery.pageNum = 1;
        // await this.initChatList(this.chatUserData, this.activeTab);
        if (this.activeType == 'all') {
          await this.initChatList(this.chatUserData, this.activeTab);
        } else {
          await this.initFileList(this.chatUserData, this.activeTab);
        }
      },

      /**
       * 切换消息类型标签页
       * @param {String} e - 消息类型(all/files)
       */
      changeTab(e) {
        if (e == 'all') {
          // 显示所有消息，使用深拷贝确保数据独立性
          this.reset();
          this.initChatList(this.chatUserData, this.activeTab);
        } else {
          // 只显示文件消息
          this.reset();
          this.initFileList(this.chatUserData, this.activeTab);
        }
      },

      /**
       * 加载更多的群聊
       */
      loadMoreGroup() {
        this.searchGroup.pageNum++;
        this.getGroupData();
      },

      /**
       * 加载更多聊天记录
       */
      async loadMoreChatList() {
        if (this.messageList.length == this.messageTotal) return;
        if (this.messageList.length < this.messageTotal) {
          this.groupQuery.pageNum++;
          console.log('🚀🥶💩~ this.groupQuery', this.groupQuery);
          // this.initChatList(this.chatUserData, this.activeTab);
          if (this.activeType == 'all') {
            await this.initChatList(this.chatUserData, this.activeTab);
          } else {
            this.fileQuery.pageNum++;
            await this.initFileList(this.chatUserData, this.activeTab);
          }
        }
      },

      /**
       * 获取用户列表
       */
      async getUserListFn() {
        this.isLoading = true;
        let { data } = await getUserList(this.queryUser);
        this.userTotal = +data.totalItems;
        this.filteredUsers = this.filteredUsers.concat(data.data);
        this.isLoading = false;
      },

      /**
       * 加载更多用户(左侧用户列表)
       */
      async loadMoreUser() {
        console.log('触底');
        let that = this;
        if (that.isLoading) return;
        that.isLoading = true;
        try {
          if (that.filteredUsers.length == that.userTotal) {
            // this.$message.warning('没有更多数据了');
            return;
          } else {
            that.queryUser.pageNum++;
            that.isLoading = false;
            await that.getUserListFn();
          }
        } catch (error) {
          console.log(error);
          that.isLoading = false;
        } finally {
          that.isLoading = false;
        }
      },

      /**
       * 加载更多用户(右侧聊天用户列表)
       */
      async loadMoreUserRight() {
        let that = this;
        if (that.chatUserListLoading) return;
        that.chatUserListLoading = true;
        try {
          if (that.chatUserTotal == that.selectUserList.length) {
            // this.$message.warning('没有更多数据了');
            return;
          } else {
            that.queryChatUser.pageNum++;
            await that.getChatUserList();
          }
        } catch (error) {
          console.log(error);
          that.chatUserListLoading = false;
        } finally {
          that.chatUserListLoading = false;
        }
      },

      /**
       * 获取聊天用户列表(单聊/群聊)
       */
      async getChatUserList() {
        let res = await getChatUserListGroup(this.queryChatUser);
        console.log(res);
        this.chatUserTotal = res.total;
        this.selectUserList = this.selectUserList.concat(res.data);
        this.chatUserListLoading = false;
      },

      /**
       * 刷新聊天记录
       */
      async refresh() {
        if (!this.groupQuery.groupId) return;
        this.groupQuery.pageNum = 1;
        this.fileQuery.pageNum = 1;
        this.messageList = [];
        if (this.activeType == 'all') {
          await this.initChatList(this.chatUserData, this.activeTab);
        } else {
          await this.initFileList(this.chatUserData, this.activeTab);
        }
      },

      /**
       * 重置查询条件
       */
      reset() {
        this.$refs.chatHistory.searchKey = '';
        this.$refs.chatHistory.filterDate = '';
        this.$refs.chatHistory.filterSender = '';
        this.groupQuery.content = '';
        this.groupQuery.times = '';
        this.groupQuery.sender = '';
        this.groupQuery.pageNum = 1;
        this.fileQuery.pageNum = 1;
        this.fileQuery.fileName = '';
        this.fileQuery.times = '';
        this.fileQuery.sender = '';
        this.messageList = [];
        this.allMessageList = []; // 同时清空所有消息的副本
      },

      /**
       * 选择聊天记录
       * @param {Object} item - 选中的用户/群组
       * @param {String} type - 聊天类型(single/group)
       */
      async selectChatList(item, type) {
        this.chatUserData = { ...item };
        this.reset();
        this.groupQuery.groupId = this.chatUserData.groupId;
        this.fileQuery.senderId = '';
        this.fileQuery.receiverId = '';
        this.fileQuery.groupId = this.chatUserData.groupId;
        let { data } = await getGroupUserList({
          groupId: this.chatUserData.groupId,
          pageNum: 1,
          pageSize: 100
        });
        this.userArr = data;
        if (this.activeType == 'all') {
          await this.initChatList(item, type);
        } else {
          console.log('🚀🥶💩~ type', type);
          await this.initFileList(item, type);
        }
      },

      /**
       * 初始化聊天列表
       * @param {Object} item - 用户/群组信息
       * @param {String} type - 聊天类型(single/group)
       */
      async initChatList(item, type) {
        let res;
        this.chatLoading = true;
        res = await getChatListGroup(this.groupQuery);
        this.messageTotal = +res.data.totalItems;
        let arr = this.messageList.concat(res.data.data);
        this.messageList = JSON.parse(JSON.stringify(arr));
        this.messageList.forEach((item) => {
          if (item.messageType == 'TIMRelayElem' && item.messageContent) {
            let obj;
            obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
            item.messageContent = obj;
          }
        });
        // 保存所有消息的副本用于筛选，确保使用深拷贝
        this.allMessageList = JSON.parse(JSON.stringify(this.messageList));
        this.chatLoading = false;
      },
      /**
       * 初始化文件列表
       * @param {Object} item - 用户/群组信息
       * @param {String} type - 聊天类型(single/group)
       */
      async initFileList(item, type) {
        console.log('🚀🥶💩~ type', type);
        let res;
        this.chatLoading = true;
        this.fileQuery.chatType = type === 'single' ? '1' : '2';
        res = await getFileList(this.fileQuery);
        this.messageTotal = +res.data.totalItems;
        let arr = this.messageList.concat(res.data.data);
        this.messageList = JSON.parse(JSON.stringify(arr));
        this.messageList.forEach((item) => {
          if (item.messageType == 'TIMRelayElem') {
            let obj;
            obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
            item.messageContent = obj;
          }
        });
        // 保存所有消息的副本用于筛选，确保使用深拷贝
        this.allMessageList = JSON.parse(JSON.stringify(this.messageList));
        this.chatLoading = false;
      },

      /**
       * 获取角色列表
       */
      async getRoleListFn() {
        let { data } = await getRoleList();
        console.log(data);
        this.roleList = data.data;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .chat-container {
    display: flex;
    height: calc(100vh - 84px);
    background-color: #f0f2f5;
    padding: 16px;
    gap: 16px;
  }

  .left-section {
    width: 30%;
    display: flex;
    gap: 16px;
    height: 100%;

    .chat-users {
      width: 1000%;
      background: #fff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  .right-section {
    width: 70%;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .chat-user-info {
    padding: 16px 24px 0 24px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    // border-bottom: 1px solid #ebeef5;

    .chat-user-name {
      font-size: 15px;
      color: #303133;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #67c23a;
        margin-right: 2px;
      }
    }

    .refresh-btn {
      padding: 6px 12px;
      font-size: 13px;
      // color: #606266;
      transition: all 0.3s;
      border-radius: 4px;

      // &:hover {
      //   color: #409eff;
      //   background-color: #ecf5ff;
      // }

      i {
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }

  .chat-header {
    padding: 12px 24px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: center;

    .el-radio-group {
      width: 240px;

      :deep(.el-radio-button__inner) {
        padding: 8px 0;
        font-size: 13px;
        border-color: #dcdfe6;

        &:hover {
          color: #409eff;
        }
      }

      :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
        background-color: #409eff;
        border-color: #409eff;
        box-shadow: -1px 0 0 0 #409eff;
      }
    }
  }

  .header {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .search-box {
        padding: 20px;
      }

      .user-avatar {
        margin-right: 12px;
        position: relative;

        :deep(.el-avatar) {
          border-radius: 6px;
          background-color: #409eff;
          color: #fff;
          font-weight: 500;
        }

        :deep(.el-tag) {
          position: absolute;
          top: 0;
          left: 0;
          transform: translateY(-50%);
          z-index: 1;
          border-radius: 2px;
          padding: 0 4px;
          height: 18px;
          line-height: 16px;
          font-size: 12px;
        }
      }

      .user-info {
        flex: 1;
        min-width: 0;

        .user-name {
          font-size: 15px;
          color: #303133;
          font-weight: 500;
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .tabs {
      display: flex;
      justify-content: center;

      .el-radio-group {
        width: 240px;

        :deep(.el-radio-button__inner) {
          padding: 8px 0;
          font-size: 13px;
          border-color: #dcdfe6;

          &:hover {
            color: #409eff;
          }
        }

        :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
          background-color: #409eff;
          border-color: #409eff;
          box-shadow: -1px 0 0 0 #409eff;
        }
      }
    }
  }

  .search-section {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    // flex-direction: column;
    gap: 12px;
    height: 10%;

    .filter-select {
      width: 50%;

      :deep(.el-input__inner) {
        border-radius: 4px;
      }
    }

    .search-input {
      width: 100%;

      :deep(.el-input__inner) {
        border-radius: 4px;

        &:focus {
          border-color: #409eff;
        }
      }

      :deep(.el-input__prefix) {
        height: 32px !important;
        left: 12px;
        color: #909399;
      }

      :deep(.el-input__inner) {
        padding-left: 36px;
      }
    }
  }

  .user-list {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    padding: 8px 0;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .higth-class {
      background: #f5f7fa;
    }

    .user-item {
      display: flex;
      align-items: center;
      padding: 12px 24px;
      cursor: pointer;
      transition: background-color 0.3s;
      .higth-class {
        background: #f5f7fa;
      }
      &:hover {
        background-color: #f5f7fa;
      }

      .user-avatar {
        margin-right: 12px;
        position: relative;

        :deep(.el-avatar) {
          border-radius: 6px;
          background-color: #409eff;
          color: #fff;
          font-weight: 500;
        }

        :deep(.el-tag) {
          position: absolute;
          top: 0;
          left: 0;
          transform: translateY(-50%);
          z-index: 1;
          border-radius: 2px;
          padding: 0 4px;
          height: 18px;
          line-height: 16px;
          font-size: 12px;
        }
      }

      .user-info {
        flex: 1;
        min-width: 0;

        .user-name {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .club-tag {
        display: inline-block;
        padding: 2px 8px;
        background-color: #f0f2f5;
        color: #909399;
        border-radius: 10px;
        font-size: 11px;
      }

      .user-action {
        margin-left: 12px;

        .el-tag {
          border-radius: 10px;
          padding: 0 8px;
          height: 20px;
          line-height: 18px;
          border: 1px solid #e4e7ed;
          background-color: #f5f7fa;
          color: #909399;
        }
      }
    }
  }

  .chat-history {
    // flex: 1;
    height: calc(100vh - 284px);
    overflow-y: auto;
  }

  .message-type-tabs {
    padding: 6px 24px;
    background: #fff;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: center;

    .el-radio-group {
      width: 240px;

      :deep(.el-radio-button__inner) {
        padding: 8px 0;
        font-size: 13px;
        border-color: #dcdfe6;

        &:hover {
          color: #409eff;
        }
      }

      :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
        background-color: #409eff;
        border-color: #409eff;
        box-shadow: -1px 0 0 0 #409eff;
      }
    }
  }
</style>

<style>
  .el-radio-button {
    width: 50%;
  }

  .el-radio-button__inner {
    width: 100%;
  }

  .el-select {
    width: 100%;
  }

  .el-input__inner {
    border-radius: 4px;
  }
</style>
