<template>
  <div class="container">
    <el-card shadow="always" :body-style="{ padding: '20px' }">
      <!-- card body -->
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="querydata" ref="querydata">
          <el-form-item label="关系组名称：" prop="friendRoleName">
            <el-input v-model.trim="querydata.friendRoleName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="关联角色：" prop="role">
            <el-select v-model="querydata.role" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in roleList" :key="index" :label="item.roleName" :value="item.roleValue"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchData">查 询</el-button>
            <el-button @click="rest">重 置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card shadow="always" :body-style="{ padding: '20px' }">
      <div slot="header">
        <div class="table-header">
          <div class="title">好友关系分组</div>
          <el-button icon="el-icon-plus" type="primary" @click="handleAdd">新 增</el-button>
        </div>
      </div>
      <!-- card body -->
      <!-- 表格区域 -->
      <div class="table-area">
        <el-table :data="tableData" border style="width: 100%" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }" v-loading="loading">
          <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
          <el-table-column width="" align="center" v-for="(item, index) in tableHeaderList" :key="index" :prop="item.value" :label="item.name">
            <template v-slot="{ row }">
              <div v-if="item.value == 'operate'">
                <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" size="small" @click="handleDelete(row)">删除</el-button>
              </div>
              <div v-else-if="item.value == 'friendRoles'">
                <el-tag style="margin-left: 10px" v-for="(item, index) in toArr(row.friendRoleIds)" :key="index">{{ item }}</el-tag>
              </div>
              <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->

        <el-row type="flex" justify="left" align="left" style="height: 60px">
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="querydata.pageNum"
              :page-sizes="[10, 20, 30, 40, 50]"
              :page-size="querydata.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </el-row>
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @open="openDialog" :close-on-click-modal="false">
      <el-form :model="formData" ref="formData" :rules="rules" label-width="120px">
        <el-form-item label="关系组名称：" prop="name">
          <el-input v-model="formData.friendRoleName" placeholder="请输入" maxlength="20" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="关联角色：" prop="role">
          <el-select v-model="formData.friendRoles" multiple placeholder="请选择" style="width: 100%" @change="handleChange">
            <!-- <el-option label="品牌" value="brand"></el-option> -->
            <el-option v-for="(item, index) in roleList" :key="index" :label="item.roleName" :value="item.roleValue"></el-option>
          </el-select>
        </el-form-item>
        <div class="tips">
          <div>角色权限：</div>
          <div>
            <p>1. 配置两个角色均可与对方可以添加好友；</p>
            <p>2. 只配置一个角色，则该角色只能添加好友；</p>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import NoMore from '@/components/NoMore/index.vue';
  import { getChatRoleList, getFriendList, addFriendList, editFriendList, delFriendList } from '@/api/roleRelationshipManagement/friendshipManagement';

  export default {
    name: 'FriendshipManagement',
    components: {
      NoMore
    },
    data() {
      return {
        // 查询参数
        querydata: {
          pageNum: 1, // 当前页码
          pageSize: 10, // 每页显示条数
          friendRoleName: '', // 关系组名称搜索条件
          role: '' // 关联角色搜索条件
        },
        total: 0, // 数据总条数
        roleList: [], // 角色列表数据
        // 表格表头配置
        tableHeaderList: [
          {
            name: '关系组名称',
            value: 'friendRoleName'
          },
          {
            name: '关联角色',
            value: 'friendRoles'
          },
          {
            name: '创建人',
            value: 'createUser'
          },
          {
            name: '创建时间',
            value: 'createTime'
          },
          {
            name: '操作',
            value: 'operate'
          }
        ],
        tableData: [], // 表格数据
        dialogVisible: false, // 弹窗显示状态
        dialogTitle: '新增关联好友关系', // 弹窗标题
        // 表单数据
        formData: {
          friendRoleName: '', // 关系组名称
          friendRoles: [] // 关联角色数组
        },
        // 表单验证规则
        rules: {
          friendRoleName: [{ required: true, message: '请输入关系组名称', trigger: 'blur' }],
          friendRoles: [{ required: true, message: '请选择关联角色', trigger: 'change' }]
        },
        loading: false
      };
    },
    created() {
      this.initData();
      this.getChatRoleListFn();
    },
    methods: {
      /**
       * 处理字符串转换为数组
       * @param {String} str - 接受的字符串
       */
      toArr(str) {
        const str1 = str.replace(/'/g, '"'); // 替换单引号为双引号
        const strArr = JSON.parse(str1);
        const result = this.roleList.filter((item1) => strArr.some((item2) => item2 == item1.id));
        // console.log(result);
        return result.map((i) => i.roleName);
      },
      /**
       * 处理角色选择变化
       * @param {Array} val - 选中的角色值数组
       */
      handleChange(val) {
        if (val.length > 2) {
          this.$message.warning('最多只能选择两个选项');
          this.formData.friendRoles = val.slice(0, 2);
        }
      },

      /**
       * 获取角色列表数据
       */
      async getChatRoleListFn() {
        let { data } = await getChatRoleList({ roleName: '', roleApp: '', pageNum: 1, pageSize: 50 });
        // console.log(data);
        this.roleList = data.data;
      },

      /**
       * 打开弹窗时清除表单验证
       */
      openDialog() {
        this.$nextTick(() => {
          this.$refs.formData.clearValidate();
        });
      },

      /**
       * 关闭弹窗并重置表单
       */
      closeDialog() {
        this.$refs.formData.resetFields();
        this.dialogVisible = false;
      },

      /**
       * 初始化表格数据
       */
      async initData() {
        try {
          this.loading = true;
          let { data } = await getFriendList(this.querydata);
          console.log(data);
          this.total = +data.totalCount;
          this.tableData = data.data;
          this.loading = false;
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      },

      /**
       * 搜索数据
       */
      searchData() {
        this.querydata.pageNum = 1;
        this.initData();
      },

      /**
       * 重置搜索条件
       */
      rest() {
        this.$refs.querydata.resetFields();
        this.searchData();
      },

      /**
       * 新增关系组
       */
      handleAdd() {
        this.dialogTitle = '新增关联好友关系';
        this.formData = {
          friendRoleName: '',
          friendRoles: []
        };
        this.dialogVisible = true;
      },

      /**
       * 编辑关系组
       * @param {Object} row - 当前行数据
       */
      handleEdit(row) {
        this.dialogTitle = '编辑关联好友关系';
        // console.log(row.friendRoleIds);
        const str = row.friendRoleIds.replace(/'/g, '"'); // 替换单引号为双引号
        const strArr = JSON.parse(str);
        let arr = this.filterArr(this.roleList, strArr);
        // console.log(arr, '===================');
        this.formData = { ...row };
        this.formData.friendRoles = arr.map((i) => i.roleValue);
        this.dialogVisible = true;
      },

      /**
       * 过滤数组，获取匹配的角色数据
       * @param {Array} arr1 - 角色列表
       * @param {Array} arr2 - 需要匹配的角色名称数组
       * @returns {Array} 匹配的角色数据数组
       */
      filterArr(arr1, arr2) {
        console.log(arr1, arr2, '=-=--=--=-=-=-=');

        const result = arr1.filter((item1) => arr2.some((item2) => item2 == item1.id));
        console.log(result);
        return result;
      },

      /**
       * 删除关系组
       * @param {Object} row - 当前行数据
       */
      handleDelete(row) {
        this.$confirm('确认删除该关系组?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            await delFriendList({ friendRoleId: row.id });
            this.$message.success('删除成功');
            this.initData();
          })
          .catch(() => {});
      },

      /**
       * 提交表单
       */
      submitForm() {
        this.$refs.formData.validate(async (valid) => {
          if (valid) {
            let obj = {};
            obj = this.formData.id
              ? { id: this.formData.id, friendRoleName: this.formData.friendRoleName, friendRoles: this.formData.friendRoles }
              : { friendRoleName: this.formData.friendRoleName, friendRoles: this.formData.friendRoles };
            console.log(obj, '11111111111111');
            this.formData.id ? await editFriendList(obj) : await addFriendList(obj);
            this.dialogVisible = false;
            this.$message.success('操作成功');
            this.initData();
          }
        });
      },

      /**
       * 处理每页显示条数变化
       * @param {Number} val - 新的每页显示条数
       */
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },

      /**
       * 处理页码变化
       * @param {Number} val - 新的页码
       */
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      },

      /**
       * 设置表格行样式
       * @param {Object} { rowIndex, columnIndex } - 行索引和列索引
       * @returns {String} 样式字符串
       */
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa;color:#4d4d4d';
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .container {
    padding: 20px;
  }

  .breadcrumb {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
  }

  .search-area {
    background: #fff;
    // padding: 20px;
    border-radius: 4px;
    // margin-bottom: 20px;
  }

  .table-area {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
  }
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }

  .tips {
    background: #f5f7fa;
    padding: 15px;
    margin-top: 20px;
    color: #666;
    font-size: 14px;
    line-height: 1.8;
    display: flex;
    align-items: flex-start;
    p {
      margin: 0;
      font-weight: 600;
    }
  }

  :deep(.el-input-group__append) {
    background-color: #fff;
    color: #999;
  }
</style>
<style>
  .el-card__header {
    border: none;
  }
</style>
