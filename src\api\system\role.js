/**
 * “角色管理”相关接口
 */
import request from '@/utils/request';

const prefix = 'im';

export default {
  /**
   * 添加角色
   * @param data
   */
  addRole(data) {
    return request({
      url: prefix + '/api/sys-role',
      method: 'post',
      data
    });
  },

  /**
   * 删除角色
   * @param data
   */
  deleteRole(data) {
    return request({
      url: prefix + '/api/sys-role/' + data,
      method: 'delete'
    });
  },

  /**
   * 查询角色
   * @param queryParam
   * @param pageParam
   */
  queryRole(query, pageParam) {
    return request({
      url: prefix + '/api/sys-role/query',
      method: 'GET',
      params: {
        rname: query.rname,
        rval: query.rval,
        pageNum: pageParam.currentPage == null ? 1 : pageParam.currentPage,
        pageSize: pageParam.size == null ? 10 : pageParam.size
      }
    });
  },

  /**
   * 更新角色
   * @param data
   */
  updateRole(data) {
    return request({
      url: prefix + '/api/sys-role/info',
      method: 'patch',
      data
    });
  },

  /**
   * 更新角色的权限
   * @param perm
   */
  updateRolePerms(data) {
    return request({
      url: prefix + '/api/role-perm',
      method: 'patch',
      data
    });
  },

  /**
   * 添加角色的权限
   * @param perm
   */
  addRolePerm(data) {
    return request({
      url: prefix + '/api/role-perm',
      method: 'post',
      data
    });
  },

  /**
   * 删除角色的权限
   * @param perm
   */
  deleteRolePerm(data) {
    return request({
      url: prefix + '/api/role-perm',
      method: 'delete',
      data
    });
  },

  /**
   * 查选角色的所有权限值
   * @param rid
   */
  findRolePerms(rid) {
    return request({
      // url: '/deliver/sys/role/perms/' + rid,
      url: prefix + '/api/sys-role/perms/' + rid,
      method: 'get'
    });
  },

  /**
   * 所有角色
   * @returns {AxiosPromise}
   */
  listRole() {
    return request({
      url: prefix + '/api/sys-role/option/list',
      method: 'GET'
    });
  },

  /**
   * 查询业务角色
   * @param data
   * @returns {*}
   */
  getRoles(data) {
    return request({
      url: prefix + '/api/role/get-roles',
      method: 'GET',
      params: data
    });
  },

  /**
   * 查询业务角色相关应用列表
   * @param data
   * @returns {*}
   */
  getRoleApp(data) {
    return request({
      url: prefix + '/api/role/get-role-app',
      method: 'GET',
      params: data
    });
  },
  /**
   * 获取关联应用列表
   * @param data
   * @returns {*}
   */
  getAppList(data) {
    return request({
      url: prefix + '/api/app/get-app-list',
      method: 'GET',
      params: data
    });
  },

  /**
   * 查询建群角色组
   * @param data
   */
  groupRole(data) {
    return request({
      url: prefix + '/api/group-role/group-roles',
      method: 'GET',
      params: data
    });
  },

  /**
   * 查询建群角色组相关app列表
   * @param data
   * @returns {*}
   */
  groupApp(data) {
    return request({
      url: prefix + '/api/group-role/group-app',
      method: 'get',
      params: data
    });
  }
};
