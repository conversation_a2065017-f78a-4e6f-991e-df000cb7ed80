import Layout from '@/layout';

const historyLog = {
  path: '/roleRelationshipManagement',
  meta: {
    title: '角色关系管理',
    icon: 'chart',
    perm: 'm:role'
  },
  component: Layout,
  children: [
    {
      path: 'businessRole',
      component: () => import('@/views/roleRelationshipManagement/businessRole/index'),
      name: 'businessRole',
      meta: { title: '业务角色', icon: 'studentList', affix: false, perm: 'm:role:businessRole' }
    },
    {
      path: 'friendshipManagement',
      component: () => import('@/views/roleRelationshipManagement/friendshipManagement/index'),
      name: 'friendshipManagement',
      meta: { title: '好友关系管理', icon: 'studentList', affix: false, perm: 'm:role:friendshipManagement' }
    }
  ]
};

export default historyLog;
