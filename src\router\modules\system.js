import Layout from '@/layout';

const systemRouter = {
  path: '/system',
  component: Layout,
  meta: {
    title: '组织架构',
    icon: 'tree',
    perm: 'm:sys'
  },
  alwaysShow: true,
  children: [
    {
      path: 'userManagement',
      component: () => import('@/views/system/user/index'),
      name: 'userManagement',
      meta: { title: '用户管理', icon: 'usermanage', affix: false, perm: 'm:sys:user' }
    },
    {
      path: 'roleTagManage',
      component: () => import('@/views/system/role/index'),
      name: 'roleTagManage',
      meta: { title: '角色管理', icon: 'usermanage', affix: false, perm: 'm:sys:role' }
    },
    {
      hidden: true,
      path: 'role_manage/:roleId/assign_perm',
      name: 'role_manage_assign_perm',
      component: () => import('@/views/system/role/assign_perm'),
      meta: {
        hiddenTag: true,
        title: '角色授权'
        // perm: 'm:sys:role_perm'
      }
    },
    {
      path: 'permManage',
      component: () => import('@/views/system/perm/index'),
      name: 'permManage',
      meta: { title: '权限管理', icon: 'usermanage', affix: false, perm: 'm:per:perm' }
    }
  ]
};

export default systemRouter;
