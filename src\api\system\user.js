/**
 * 登录相关接口
 */
import request from '@/utils/request';

const prefix = 'im';

export default {
  getUser(data) {
    return request({
      url: prefix + '/api/sys-user/get-list',
      method: 'get',
      params: data
    });
  },

  /**
   * 获取手机号
   * @param data
   * @returns {*}
   */
  getPhone(data) {
    return request({
      url: prefix + '/api/sys-user/getLoginPhone',
      method: 'get',
      params: data
    });
  },

  /**
   *修改用户
   */
  updateUser(data) {
    return request({
      url: prefix + '/api/sys-user/edit-user',
      method: 'put',
      data
    });
  },

  /**
   *修改用户角色
   */
  updateUserRole(data) {
    return request({
      url: prefix + '/api/sys-user/edit-role',
      method: 'patch',
      data
    });
  },

  /**
   *新增用户
   */
  addUser(data) {
    return request({
      url: prefix + '/api/sys-user/add-user',
      method: 'post',
      data
    });
  },
  /**
   *删除用户
   */
  deleteUser(id) {
    return request({
      url: prefix + '/api/sys-user/delete-user?id=' + id,
      method: 'delete'
    });
  },
  /**
   * 组织结构树
   */
  getOrgTree() {
    return request({
      url: prefix + '/api/sys-dept/get-tree',
      method: 'get'
    });
  },
  /**
   *新增子部门
   */
  addChildDept(data) {
    return request({
      url: prefix + '/api/sys-dept/add-dept',
      method: 'post',
      data
    });
  },
  /**
   * 编辑部门
   */
  editDept(data) {
    return request({
      url: prefix + '/api/sys-dept/update-dept',
      method: 'put',
      data
    });
  },
  /**
   * 删除部门
   */
  deleteDept(id) {
    return request({
      url: prefix + '/api/sys-dept/delete-dept?id=' + id,
      method: 'delete'
    });
  }
};
