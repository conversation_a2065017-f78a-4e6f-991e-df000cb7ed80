/** * 聊天记录详情抽屉组件 * @component DrawContent * @description 用于显示投诉相关的聊天记录详情，包含聊天记录列表、用户信息、处理结果等功能 */
<template>
  <el-drawer :before-close="handleClose" :direction="direction" :visible.sync="drawer" :with-header="false" size="75%" title="记录详情">
    <div class="chat-record-container">
      <!-- 头部信息 -->
      <div class="header-title" style="display: flex">
        <h2>记录详情</h2>
        <div class="header">
          <div v-if="chatType === 'groupChat'" class="user-info">
            <span class="chat-title" style="margin-right: 4px; font-size: 16px">{{ getInitData.complaintUserName }}</span>
            <el-tag style="margin-right: 4px">{{ getInitData.roleName }}</el-tag>
            <span class="chat-title" style="margin-right: 4px; font-size: 16px">在{{ groupName }}的</span>
            <span class="chat-subtitle">聊天记录</span>
          </div>
          <div v-else class="user-info">
            <span class="chat-title" style="margin-right: 4px; font-size: 16px">{{ userArr[0].nickname || '' }}</span>
            <el-tag style="margin-right: 4px">{{ userArr[0].role || '' }}</el-tag>
            <span class="chat-title" style="margin-right: 4px; font-size: 16px">
              和{{ userArr[1].nickname || '' }}
              <el-tag style="margin-right: 4px">{{ userArr[1].role || '' }}</el-tag>
              的
            </span>
            <span class="chat-subtitle">聊天记录</span>
          </div>
        </div>
      </div>
      <div class="icon-box">
        <i class="el-icon-refresh-right" style="font-size: 24px" @click="initData" />
        <i class="el-icon-close" style="font-size: 24px; margin-left: 20px; margin-right: 12px" @click="handleClose" />
      </div>
    </div>

    <div class="main-content">
      <div v-loading="chatLoading" class="chat-list-left">
        <div class="message-type-tabs" style="">
          <el-radio-group v-model="activeType" @change="changeTab">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="files">文件</el-radio-button>
          </el-radio-group>
        </div>
        <ChatHistory
          ref="chatHistoryRef"
          :activeType="activeType"
          :distance="10"
          :messageList="messageList"
          :self="selectedUser"
          :userList="userArr"
          @filterChange="filterChangeChatList"
          @loadMore="loadMoreChatList"
        ></ChatHistory>
      </div>
      <div class="desc-right">
        <div class="desc-nessage">
          <div class="chat-info">
            <el-avatar :size="60" :src="getInitData.complaintUserUrl"></el-avatar>
            <div class="desc-info">
              <div class="name">
                <div>
                  <el-tag style="margin-right: 10px">{{ getInitData.roleName }}</el-tag>
                </div>
                <div>{{ getInitData.complaintUserName }}</div>
              </div>

              <div class="phone">{{ getInitData.complaintUserCode }}</div>
            </div>
          </div>
          <div class="chat-list-count">
            用户选择
            <div style="color: red">{{ getInitData.complaintNumber || 0 }}</div>
            条聊天记录
            <!--            <el-buttonsize="mini" type="primary" ></el-button>-->
            <el-button class="button-style" style="" type="primary" @click="isViewChat = !isViewChat">
              {{ isViewChat ? '关闭投诉记录' : '查看投诉聊天' }}
            </el-button>
          </div>
          <div v-if="complaintList.length > 0 && isViewChat" class="complaint-list">
            <ShowComplaint :messageList="complaintList" :self="selectedUser" />
          </div>

          <h2 class="user-desc">用户描述</h2>
          <div style="color: #9a9a9a">
            {{ getInitData.complaintUserDescription }}
          </div>
        </div>
        <div v-if="getInitData.processStatus === '待处理'" class="resut-submit">
          <h2 style="margin: 0 0 10px; font-size: 16px">处理结果</h2>
          <el-input
            v-model="resultat"
            :autosize="{ minRows: 4, maxRows: 8 }"
            :maxlength="100"
            :show-word-limit="true"
            clearable
            placeholder="请输入处理结果，处理结果将通过APP消息反馈给投诉人。"
            type="textarea"
          />

          <el-button style="float: right; margin-top: 10px" type="primary" @click="handleSubmit">确定</el-button>
        </div>
        <div v-else class="resut-content">
          <h2 style="font-size: 16px">处理结果</h2>
          <br />
          <div
            style="
              color: #9a9a9a;
              width: 100%;
              word-break: break-all; /* 强制所有单词/字符换行 */
              word-wrap: break-word; /* 只在必要时换行（更美观） */
              overflow-wrap: anywhere !important; /* 更智能的换行方式 */
            "
          >
            {{ getInitData.processResult }}
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
  import ChatHistory from '@/components/ChatHistory/index.vue';
  import NoMore from '@/components/NoMore/index.vue';
  import informationApi from '@/api/riskAudit/complaintManage';
  import riskSetting from '@/api/riskAudit/riskSetting';
  import { getChatListGroup, getChatListSingle, getFileList, getGroupInfoApi, getGroupUserList } from '@/api/chatContent/chatFile';
  import ShowComplaint from '@/views/riskAudit/complaintManage/components/ShowComplaint.vue';

  export default {
    name: 'DrawContent',
    components: { ShowComplaint, NoMore, ChatHistory },
    data() {
      return {
        isViewChat: false,
        /**
         * 处理结果文本内容
         * @type {string}
         */
        resultat: '',
        /**
         * 聊天用户数据
         * @type {Object}
         * @property {string} id - 用户ID
         */
        chatUserData: {
          id: ''
        },
        /**
         * 处理结果内容
         * @type {string}
         */
        processResult: '经过核实，我们将对该员工执行处罚措施，措施如下...',
        /**
         * 选中的用户信息
         * @type {Object}
         */
        selectedUser: {
          tencentId: '123123123',
          nickname: '小明'
        },
        /**
         * 当前激活的消息类型
         * @type {string}
         */
        activeType: 'all',
        /**
         * 消息列表
         * @type {Array}
         */
        messageList: [],
        /**
         * 消息总数
         * @type {number}
         */
        messageTotal: 0,
        /**
         * 初始化数据
         * @type {Object}
         */
        getInitData: {
          complaintUserDescription: '',
          roleName: '',
          complaintUserName: '',
          complaintUserUrl: '',
          complaintUserCode: '',
          complaintNumber: '',
          processStatus: '',
          processResult: ''
        },
        /**
         * 抽屉组件显示状态
         * @type {boolean}
         */
        drawer: false,
        /**
         * 抽屉组件方向
         * @type {string}
         */
        direction: 'rtl',
        /**
         * 群组名称
         * @type {string}
         */
        groupName: '',
        /**
         * 接收者ID
         * @type {string}
         */
        receiverId: '',
        /**
         * 发送者ID
         * @type {string}
         */
        senderId: '',
        /**
         * 聊天类型
         * @type {string}
         */
        chatType: 'single',
        /**
         * 单聊查询参数
         * @type {Object}
         * @property {string} senderId - 发送者ID
         * @property {string} receiverId - 接收者ID
         * @property {string} content - 内容
         * @property {string} times - 时间
         * @property {number} pageNum - 页码
         * @property {number} pageSize - 每页数量
         */
        singleQuery: {
          senderId: '',
          sender: '',
          receiverId: '',
          content: '',
          times: '',
          pageNum: 1,
          pageSize: 10
        },
        /**
         * 群聊查询参数
         * @type {Object}
         * @property {string} sender - 发送者ID
         * @property {string} groupId - 群组ID
         * @property {string} content - 内容
         * @property {string} times - 时间
         * @property {number} pageNum - 页码
         * @property {number} pageSize - 每页数量
         */
        groupQuery: {
          sender: '',
          groupId: sessionStorage.getItem('groupId'),
          content: '',
          times: '',
          pageNum: 1,
          pageSize: 10
        },
        /**
         * 用户列表
         * @type {Array}
         */
        userArr: [
          {
            nickname: '',
            role: ''
          },
          {
            nickname: '',
            role: ''
          }
        ],
        singleGroupData: {},
        dialogVisible: false,
        complaintList: [],
        chatLoading: false,
        singleFileQuery: {
          senderId: '',
          receiverId: '',
          fileName: '',
          fileType: '',
          times: '',
          sender: '',
          pageNum: 1,
          pageSize: 10,
          chatType: 1
        },
        groupFileQuery: {
          fileName: '',
          fileType: '',
          times: '',
          sender: '',
          groupId: sessionStorage.getItem('groupId'),
          pageNum: 1,
          pageSize: 10,
          chatType: 2
        }
      };
    },
    methods: {
      async initData() {
        if (this.chatLoading) return;
        this.chatLoading = true;
        this.activeType = 'all';
        this.messageList = [];
        this.messageTotal = 0;
        let messageItem = JSON.parse(sessionStorage.getItem('messageItem'));
        console.log('🚀🥶💩~ messageItem', messageItem);
        if (messageItem.chatType === '1') {
          this.chatType = 'single';
          this.singleQuery.pageNum = 1;
          this.singleQuery.pageSize = 10;
          this.groupQuery.pageNum = 1;
          await this.getSingleMessage(messageItem);
          return;
        }
        this.chatType = 'group';
        this.groupQuery.groupId = messageItem.groupId;
        this.groupQuery.pageNum = 1;
        this.groupQuery.pageSize = 10;
        await this.getGroupMessage(messageItem);
        this.chatLoading = false;
      },

      /**
       * 获取群成员信息
       * @returns {Promise<void>}
       */
      async getGroupMenber() {
        const groupId = sessionStorage.getItem('groupId');
        let { data } = await getGroupUserList({
          groupId: groupId,
          pageNum: 1,
          pageSize: 100
        });
        this.userArr = data;
      },

      /**
       * 获取单聊用户信息
       * @param row
       * @returns {Promise<void>}
       */
      async getSinglePerson(row) {
        let params = {
          tenantIds: [row.receiverId, row.senderId]
        };
        try {
          const { code, data } = await riskSetting.getChatUserListSingle(params);
          if (code === 20000) {
            this.userArr = [
              {
                nickname: data[0].nickname,
                tencentId: data[0].tencentId,
                role: data[0].role
              },
              {
                nickname: data[1].nickname,
                tencentId: data[1].tencentId,
                role: data[1].role
              }
            ];

            // 存储聊天人员的信息
            this.singleGroupData = {
              senderId: data[0].tencentId,
              receiverId: data[1].tencentId
            };
          }
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
        }
      },

      /**
       * 群组过滤消息获取
       */
      async groupFilter(searchParams) {
        console.log('🚀🥶💩~ this.activeType', this.activeType);
        this.messageList = [];
        if (this.activeType === 'files') {
          // await this.initFileList(params);
          const res = await getFileList(this.groupFileQuery);
          this.messageTotal = +res.data.totalItems;
          if (res.data.data.length === 0) {
            this.messageList = [];
            this.messageTotal = 0;
          } else {
            let arr = this.messageList.concat(res.data.data);
            this.messageList = JSON.parse(JSON.stringify(arr));
            this.messageList.forEach((item) => {
              if (item.messageType == 'TIMRelayElem') {
                let obj;
                obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
                item.messageContent = obj;
              }
            });
          }
          this.chatLoading = false;
          return;
        }
        const res = await getChatListGroup(this.groupQuery);
        this.messageTotal = +res.data.totalItems;
        if (res.data.data.length === 0) {
          this.messageList = [];
          this.messageTotal = 0;
        } else {
          let arr = this.messageList.concat(res.data.data);
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType == 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
        }

        this.chatLoading = false;
      },
      /**
       * 获取群聊信息
       */
      async getGroupMessage() {
        console.log('加载更多群消息🚀🥶💩~ this.groupQuery', this.groupQuery);
        this.groupQuery.groupId = sessionStorage.getItem('groupId');
        const res = await getChatListGroup(this.groupQuery);
        this.messageTotal = +res.data.totalItems;
        if (res.data.data.length === 0) {
          this.messageList = [];
          this.messageTotal = 0;
        } else {
          let arr = this.messageList.concat(res.data.data);
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType == 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
        }
        this.chatLoading = false;
      },
      /**
       * 过滤聊天列表
       * @param {Object} data - 过滤条件
       */
      filterChangeChatList(data) {
        this.groupQuery.pageNum = 1;
        this.messageList = [];
        this.messageTotal = 0;
        let type = '';
        if (this.messageList?.length === 0) {
          let messageItem = JSON.parse(sessionStorage.getItem('messageItem'));
          type = messageItem.chatType === '1' ? 'single' : 'group';
        } else {
          type = this.messageList?.[0].chatType === '1' ? 'single' : 'group';
        }
        if (type === 'group') {
          this.groupQuery.content = data.content;
          this.groupQuery.times = data.times;
          this.groupQuery.sender = data.senderId;
          if (this.activeType === 'files') {
            this.groupFileQuery.pageNum = 1;
            this.groupFileQuery.fileName = data.content;
            this.groupFileQuery.sender = data.senderId;
            this.groupFileQuery.fileType = data.fileType;
            this.groupFileQuery.times = data.times;
            this.groupFileQuery.chatType = 2;
            this.groupFilter(data);
            return;
          }
          this.groupFilter(data);
        } else {
          this.singleQuery.pageNum = 1;
          this.singleQuery.sender = data.senderId;
          this.singleQuery.times = data.times;
          this.singleQuery.content = data.content;
          if (this.activeType === 'files') {
            this.singleFileQuery.pageNum = 1;
            this.singleFileQuery.fileName = data.content;
            this.singleFileQuery.sender = data.senderId;
            this.singleFileQuery.fileType = data.fileType;
            this.singleFileQuery.times = data.times;
            this.groupFileQuery.chatType = 1;
            this.singleFilter(data);
            return;
          }
          this.getSingleMessage(data);
        }
      },

      /**
       * 切换标签页
       * @param {string} e - 标签页类型
       */
      async changeTab(e) {
        this.singleQuery.content = '';
        this.singleQuery.times = '';
        this.singleQuery.pageNum = 1;
        this.groupQuery.pageNum = 1;
        this.singleFileQuery.pageNum = 1;
        this.$refs.chatHistoryRef.filterDate = '';
        this.$refs.chatHistoryRef.filterSender = '';
        this.$refs.chatHistoryRef.searchKey = '';
        this.messageList = []; // 切换tab类型时应该将消息列表置空
        this.resetSearchParams();
        this.$refs.chatHistoryRef.deleteHlightedMessages();
        const messageItem = JSON.parse(sessionStorage.getItem('messageItem'));
        // console.log('🚀🥶💩~ messageItem', messageItem);
        if (messageItem.chatType === '1') {
          // 单聊 全部聊天信息
          if (e === 'all') {
            this.groupQuery.pageNum = 1;
            await this.getSingleMessage(messageItem);
            return;
          }
          this.singleFileQuery.senderId = String(this.userArr[0].tencentId);
          this.singleFileQuery.receiverId = String(this.userArr[1].tencentId);
          this.singleFileQuery.chatType = 1;
          const res = await getFileList(this.singleFileQuery);
          this.messageTotal = +res.data.totalItems;
          if (res.data.data.length === 0) {
            this.messageList = [];
          } else {
            let arr = this.messageList.concat(res.data.data);
            this.messageList = JSON.parse(JSON.stringify(arr));
            this.messageList.forEach((item) => {
              if (item.messageType == 'TIMRelayElem') {
                let obj;
                obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
                item.messageContent = obj;
              }
            });
            // 保存所有消息的副本用于筛选，确保使用深拷贝
            this.allMessageList = JSON.parse(JSON.stringify(this.messageList));
          }

          return;
        }

        // 群聊 全部聊天信息
        if (e === 'all') {
          this.groupQuery.pageNum = 1;
          console.log('🚀🥶💩~ 应该获取群聊的消息');
          await this.getGroupMessage(messageItem);
          return;
        }
        //   此时是群聊且文件数据
        this.groupFileQuery.pageNum = 1;
        console.log('🚀🥶💩~ 应该获取文件列表');
        // 群聊 文件聊天信息
        this.groupFileQuery.groupId = sessionStorage.getItem('groupId');
        this.singleFileQuery.chatType = 2;
        console.log('此时是群聊且文件数据🚀🥶💩~ this.groupFileQuery', this.groupFileQuery);
        const res = await getFileList(this.groupFileQuery);
        console.log('🚀🥶💩~ res.data', res.data);
        this.messageTotal = +res.data.totalItems;
        if (res.data.data.length === 0) {
          this.messageList = [];
          this.messageTotal = 0;
        } else {
          let arr = this.messageList.concat(res.data.data);
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType === 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
        }
      },

      /**
       * 加载更多聊天记录
       * @description 当滚动到底部时加载更多聊天记录
       */
      async loadMoreChatList() {
        // console.log('触发了');
        if (this.chatLoading) return;
        if (this.messageList.length === this.messageTotal) return;
        this.chatLoading = true;
        if (this.messageList.length < this.messageTotal) {
          this.groupQuery.pageNum++;
          let messageItem = JSON.parse(sessionStorage.getItem('messageItem'));
          if (messageItem.chatType === '1') {
            this.singleQuery.pageNum++;
            console.log('类型🚀🥶💩~ this.activeType', this.activeType);
            if (this.activeType === 'all') {
              await this.getSingleMessage();
              console.log('🚀🥶💩~ 单聊的消息 更多');
              return;
            }
            this.singleFileQuery.pageNum++;
            // 获取文件列表
            const { code, data } = await getFileList(this.singleFileQuery);
            if (code === 20000) {
              let arr = this.messageList.concat(data.data);
              this.messageList = JSON.parse(JSON.stringify(arr));
              this.messageList.forEach((item) => {
                if (item.messageType == 'TIMRelayElem') {
                  let obj;
                  obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
                  item.messageContent = obj;
                }
              });
              this.chatLoading = false;
            }

            this.chatLoading = false;
            return;
          }

          if (this.activeType === 'all') {
            await this.getGroupMessage(messageItem, 1);
            return;
          }
          console.log('🚀🥶💩~ 应该获取群聊的文件列表的消息');
          //todo 群聊且文件
          this.groupFileQuery.pageNum++;
          this.groupFileQuery.groupId = sessionStorage.getItem('groupId');
          console.log('🚀🥶💩~ this.groupFileQuery', this.groupFileQuery);
          const res = await getFileList(this.groupFileQuery);
          this.messageTotal = +res.data.totalItems;
          if (res.data.data.length === 0) {
            this.messageList = [];
          } else {
            let arr = this.messageList.concat(res.data.data);
            this.messageList = JSON.parse(JSON.stringify(arr));
            this.messageList.forEach((item) => {
              if (item.messageType == 'TIMRelayElem') {
                let obj;
                obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
                item.messageContent = obj;
              }
            });
            this.chatLoading = false;

            // 保存所有消息的副本用于筛选，确保使用深拷贝
            this.allMessageList = JSON.parse(JSON.stringify(this.messageList));
            // this.getGroupMessage(messageItem, 1);
          }
        }
        this.chatLoading = false;
      },

      /**
       * 关闭抽屉
       */
      handleClose() {
        this.drawer = false;
        this.singleQuery.content = '';
        this.singleQuery.times = '';
        this.singleQuery.pageNum = 1;
        this.$refs.chatHistoryRef.filterDate = '';
        this.$refs.chatHistoryRef.filterSender = '';
        this.$refs.chatHistoryRef.searchKey = '';
        this.messageList = [];
        this.complaintList = [];
        this.activeType = 'all';
        this.isViewChat = false;
        console.log('🚀🥶💩~ 哈哈哈哈', 1);
        this.resultat = '';
        this.resetSearchParams();
      },
      //  重置搜索参数
      resetSearchParams() {
        // 单聊
        this.singleQuery.content = '';
        this.singleQuery.sender = '';
        this.singleQuery.times = '';
        this.singleQuery.pageNum = 1;
        this.singleQuery.pageSize = 10;
        // 群聊
        this.groupQuery.content = '';
        this.groupQuery.times = '';
        this.groupQuery.sender = '';
        this.groupQuery.groupId = '';
        this.groupQuery.pageNum = 1;
        this.groupQuery.pageSize = 10;

        // 文件
        this.singleFileQuery = {
          content: '',
          times: '',
          sender: '',
          groupId: '',
          pageNum: 1,
          pageSize: 10,
          chatType: 1
        };
        this.groupFileQuery.chatType = 2;
      },

      /**
       * 提交处理结果
       * @async
       * @description 提交投诉处理结果到后端
       */
      async handleSubmit() {
        try {
          const { code } = await informationApi.replyInformation({
            id: this.getInitData.id,
            processResult: this.resultat
          });
          if (code === 20000) {
            this.$message.success('提交成功');
            this.getInitData.processStatus = '已处理';
            this.getInitData.processResult = this.resultat;
            this.resultat = '';
            this.$emit('changeStatus');
          }
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
        }
      },

      // 单聊文件过滤
      async singleFilter() {
        this.chatLoading = true;
        console.log('单聊文件过滤🚀🥶💩~ this.singleFileQuery', this.singleFileQuery);
        this.singleFileQuery.senderId = this.userArr[0].tencentId;
        this.singleFileQuery.receiverId = this.userArr[1].tencentId;
        const res = await getFileList(this.singleFileQuery);
        this.messageTotal = +res.data.totalItems;
        if (res.data.data.length === 0) {
          this.messageList = [];
        } else {
          let arr = this.messageList.concat(res.data.data);
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType == 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
        }
        this.chatLoading = false;
      },
      // 获取单聊聊天记录
      async getSingleMessage() {
        this.chatLoading = true;
        this.singleQuery.senderId = String(this.userArr[0].tencentId);
        this.singleQuery.receiverId = String(this.userArr[1].tencentId);
        console.log('获取单聊聊天记录🚀🥶💩~ this.singleQuery', this.singleQuery);
        const res = await getChatListSingle(this.singleQuery);
        this.messageTotal = +res.data.totalItems;
        if (res.data.data.length === 0) {
          this.messageList = [];
        } else {
          let arr = this.messageList.concat(res.data.data);
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType == 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
        }
        this.chatLoading = false;
      },

      /**
       * 获取群聊信息
       * @returns {Promise<void>}
       */
      async getGroupInfo() {
        let params = {
          groupId: sessionStorage.getItem('groupId')
        };
        const res = await getGroupInfoApi(params);
        this.groupName = res.data.groupName || '';
      },

      /**
       * 获取消息列表
       * @async
       * @param row
       * @param {string} complaintCode - 投诉编号
       */
      async getMessage(row, complaintCode) {
        this.getInitData = row;
        this.$refs.chatHistoryRef?.deleteHlightedMessages();

        this.groupQuery.pageNum = 1;
        this.groupQuery.groupId = sessionStorage.getItem('groupId');
        try {
          this.chatLoading = true;
          const { code, data } = await informationApi.getMessageList({ complaintCode });
          if (code === 20000) {
            if (data.data.length === 0) {
              this.$message.warning('聊天消息正在处理中，请稍后~');
              this.chatLoading = false;
              return;
            }
            this.complaintList = data.data || [];
            this.complaintList.forEach((item) => {
              if (item.messageType == 'TIMRelayElem') {
                let obj;
                obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
                item.messageContent = obj;
              }
            });
            let messageItem = data.data[0];
            sessionStorage.setItem('groupId', messageItem.groupId);
            sessionStorage.setItem('messageItem', JSON.stringify(messageItem));
            if (messageItem.chatType === '2') {
              await this.getGroupMenber();
              await this.getGroupInfo();
            }
            this.chatType = messageItem.chatType === '1' ? 'single' : 'groupChat'; // 1单聊 2 群聊
            if (this.chatType === 'single') {
              await this.getSinglePerson(messageItem);
              await this.getSingleMessage(messageItem);
              return;
            }
            if (messageItem) {
              await this.getGroupMessage(messageItem);
            }
          }
          this.chatLoading = false;
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
          this.messageList = [];
          this.chatLoading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .chat-record-container {
    padding: 10px 20px;
    margin: 0 auto;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    display: flex;
    justify-content: space-between;
    align-content: center;
    border-bottom: 1px solid #bbbbbb;

    .header-title {
      width: 60%;
      display: flex;
      justify-content: space-between;

      h2 {
        font-size: 18px;
        color: #333;
        margin-bottom: 15px;
        width: 15%;
      }

      .header {
        display: flex;
        width: 90%;

        .user-info {
          display: flex;
          align-items: center;
          font-size: 20px;
          justify-content: center;
          margin: 0 auto;

          .chat-title,
          .chat-subtitle {
            color: #000000;
            font-size: 16px;
          }
        }
      }
    }

    .icon-box {
      display: flex;
      align-items: center;
    }
  }

  .main-content {
    display: flex;
    height: calc(100vh - 84px);
    position: relative;

    .chat-list-left {
      width: 60%;
      overflow-y: auto;
      height: calc(100vh - 84px);
      border-right: 1px solid #bbbbbb;

      .sticky-tabs {
        position: sticky;
        top: 0; /* 距离顶部的距离 */
        z-index: 100; /* 确保在其他内容之上 */
        background: white; /* 背景色，避免透明 */
        padding: 10px 0; /* 内边距 */
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); /* 可选：滚动时添加阴影 */
      }

      .message-type-tabs {
        padding: 6px 24px;
        background: #fff;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: center;

        .el-radio-group {
          width: 240px;

          :deep(.el-radio-button__inner) {
            padding: 8px 0;
            font-size: 13px;
            border-color: #dcdfe6;

            &:hover {
              color: #409eff;
            }
          }

          :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
            background-color: #409eff;
            border-color: #409eff;
            box-shadow: -1px 0 0 0 #409eff;
          }
        }
      }

      .top {
        margin: 10px;
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }

    .desc-right {
      width: 40%;
      height: 100vh;

      .desc-nessage {
        min-height: 35%;
        width: 100%;
        padding: 20px;
        border-bottom: 1px solid #bbbbbb;

        .chat-info {
          display: flex;

          .desc-info {
            margin-left: 10px;
            height: 60px;
            line-height: 1;
            align-content: center;

            .name {
              height: 30px;
              line-height: 30px;
              display: flex;
              align-content: center;
            }

            .phone {
              height: 30px;
              line-height: 30px;
            }
          }
        }

        .chat-list-count {
          height: 40px;
          line-height: 40px;
          font-size: 14px;
          margin-top: 18px;
          margin-bottom: 10px;
          display: flex;
          align-content: center;

          .button-style {
            margin-left: 14px;
            width: 100px;
            padding: 4px;
            height: 30px;
            float: right;
            margin-top: 4px;
          }
        }

        .complaint-list {
          overflow-y: auto;
        }

        .user-desc {
          margin-top: 10px;
          font-size: 16px;
        }
      }

      .resut-submit {
        height: 30%;
        width: 100%;
        padding: 20px;
      }

      .resut-content {
        height: 35%;
        width: 100%;
        padding: 20px;
      }
    }
  }
</style>
