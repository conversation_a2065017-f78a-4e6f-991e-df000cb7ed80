export const pageParamNames = ['currentPage', 'totalPage', 'totalItems'];
export const permType = {
  MENU: 1,
  BUTTON: 2,
  API: 3
};

/**
 * 下拉选择框数据：权限类型
 *
 */
export const permTypeOptions = [
  { value: permType.MENU, label: '菜单' },
  { value: permType.BUTTON, label: '按钮' },
  { value: permType.API, label: '接口' }
];

/**
 * 权限类型
 * @type {Map<any, any>}
 */
export const permTypeMap = new Map([
  [permType.MENU, '菜单'],
  [permType.BUTTON, '按钮'],
  [permType.API, '测试']
]);

export const confirm = {
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  type: 'warning'
};
export const dxSource = 'IM##BROWSER##WEB';
// 线上
// export const baseUrl = 'https://gateway.dxznjy.com';
// 本地
// export const baseUrl = 'http://lin-guokang.ngrok.dxznjy.com'; //林国康
// export const baseUrl = 'http://192.168.40.114:8081'; //陈奇荣
// export const baseUrl = 'http://192.168.40.17:8081'; //吴月龚
// export const baseUrl = 'https://dxcs199.ngrok.dxznjy.com'; //桂书新
export const baseUrl = 'https://test179.ngrok.dxznjy.com'; //测试
// export const baseUrl = 'https://uat-gateway.dxznjy.com'; //测试
export const root = {
  rval: 'root',
  pval: '*'
};
