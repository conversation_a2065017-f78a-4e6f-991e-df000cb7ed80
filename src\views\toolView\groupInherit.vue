<template>
  <div class="group-inheritance">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>群继承</span>
      </div>

      <div class="content">
        <div class="description">
          当客户群有变更时，企业可将客户群分配给其他成员继承提供服务。
          <div class="notice">新群主只能看历史90天的记录</div>
        </div>

        <div class="form-section">
          <div class="form-item">
            <span class="label">应用</span>
            <el-select v-model="appName" placeholder="请选择" style="width: 200px; margin-left: 10px" @change="changeApp" clearable>
              <el-option v-for="(item, index) in appLiat" :key="index" :label="item.appName" :value="item.appValue"></el-option>
            </el-select>
          </div>

          <div class="form-item">
            <div class="label">需要转接的客户群</div>
            <div class="group-select">
              <el-button type="text" icon="el-icon-plus" @click="showSelectGroupTypeDialog">去选择</el-button>
              <div v-if="selectedGroup" class="selected-group">
                <span>{{ selectedGroup }}</span>
                <el-button type="text" class="refresh-btn" @click="showSelectGroupTypeDialog1">重新选择</el-button>
              </div>
            </div>
          </div>

          <div class="form-item">
            <div class="label">将客户群转接给</div>
            <div class="staff-select">
              <el-button type="text" icon="el-icon-plus" @click="showSelectUserDialog">选择接替人员</el-button>
              <div v-if="selectedUserText" class="selected-staff">
                <span>{{ selectedUserText }}</span>
                <el-button type="text" class="refresh-btn" @click="showSelectUserDialog1">重新选择</el-button>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <el-button type="primary" @click="handleSubmit">确定</el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 选择客户群方式弹窗 -->
    <el-dialog title="选择需要转接的客户群" :visible.sync="groupTypeDialogVisible" width="600px" :close-on-click-modal="false" custom-class="group-type-dialog">
      <div class="group-type-container">
        <div class="group-type-item" @click="handleSelectStaff">
          <div class="type-title">接入员选择</div>
          <div class="type-desc">按需要转移群主</div>
        </div>
        <div class="group-type-item" @click="showSelectGroupDialog">
          <div class="type-title">选择指定客户群</div>
          <div class="type-desc">直接选择有变更的客户群</div>
        </div>
      </div>
    </el-dialog>
    <!-- 选择接替群主弹窗 -->
    <el-dialog title="选择人员" :visible.sync="ownerDialogVisible" width="800px" :close-on-click-modal="false" custom-class="select-staff-dialog">
      <div class="search-bar">
        <el-input v-model="queryOwner.userName" placeholder="请输入关键词" prefix-icon="el-icon-search" @change="serchOwner" clearable></el-input>
      </div>
      <el-table :data="ownerList" style="width: 100%" @selection-change="handleOwnerChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column v-for="(item, index) in ownerTable" :key="index" :prop="item.value" :label="item.name" width="">
          <template v-slot="{ row }">
            <el-avatar :size="40" :src="row.userAvatar" v-if="item.value == 'userAvatar'">
              {{ row.userAvatar ? row.nickname.charAt(0) : '' }}
            </el-avatar>
            <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange1"
          @current-change="handleCurrentChange1"
          :current-page="queryOwner.pageNum"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="queryOwner.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="ownerTotal"
        ></el-pagination>
      </div>
      <div class="table-footer">
        <span class="selected-count">已选择接替群主：{{ selectedOwnerCount }}</span>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeOwnerDialog">取消</el-button>
        <el-button type="primary" @click="confirmOwner">确定</el-button>
      </div>
    </el-dialog>

    <!-- 选择指定客户群弹窗 -->
    <el-dialog title="选择客户群" :visible.sync="groupDialogVisible" width="800px" :close-on-click-modal="false" custom-class="select-group-dialog">
      <div class="search-bar">
        <el-input v-model="queryGroup.groupName" placeholder="搜索群组" prefix-icon="el-icon-search" @change="serchGroup" clearable></el-input>
      </div>

      <div class="group-tree">
        <el-table :data="groupList" style="width: 100%" @selection-change="handleGroupSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
          <el-table-column v-for="(item, index) in groupTable" :key="index" :prop="item.value" :label="item.name" width="">
            <template v-slot="{ row }">
              <div v-if="item.value == 'owner'">
                <span>{{ row.owner && row.owner.userName }}</span>
              </div>
              <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange2"
          @current-change="handleCurrentChange2"
          :current-page="queryGroup.pageNum"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="queryGroup.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="groupTotal"
        ></el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeGroupDialog">取消</el-button>
        <el-button type="primary" @click="confirmSelectGroup">确定</el-button>
      </div>
    </el-dialog>

    <!-- 选择指定群主的客户群弹窗 -->
    <el-dialog title="选择客户群" :visible.sync="groupByOwnerDialogVisible" width="800px" :close-on-click-modal="false" custom-class="select-group-dialog">
      <div class="search-bar">
        <el-input v-model="groupByOwnerQuery.groupName" placeholder="搜索群组" prefix-icon="el-icon-search" @change="serchOwnerGroup" clearable></el-input>
      </div>

      <div class="group-tree">
        <el-table :data="groupByOwnerList" style="width: 100%" @selection-change="handleOwnerGroupSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
          <el-table-column v-for="(item, index) in groupTable" :key="index" :prop="item.value" :label="item.name" width="">
            <template v-slot="{ row }">
              <div v-if="item.value == 'owner'">
                <span>{{ row.owner && row.owner.userName }}</span>
              </div>
              <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange3"
          @current-change="handleCurrentChange3"
          :current-page="groupByOwnerQuery.pageNum"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="groupByOwnerQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="groupTotal"
        ></el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeOwnerGroup">取消</el-button>
        <el-button type="primary" @click="confirmOwnerGroup">确定</el-button>
      </div>
    </el-dialog>

    <!-- 选择接替人员弹窗 -->
    <el-dialog title="选择接替人员" :visible.sync="userDialogVisible" width="800px" :close-on-click-modal="false" @close="closeUserDialog" custom-class="select-staff-dialog">
      <div class="search-bar">
        <el-input v-model="queryUser.userName" placeholder="请输入关键词" prefix-icon="el-icon-search" @change="serchUserList" clearable></el-input>
      </div>
      <el-table :data="userList" style="width: 100%" ref="singleSelectTable" @select="handleSelect" row-key="id">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column v-for="(item, index) in userTable" :key="index" :prop="item.value" :label="item.name" width="">
          <template v-slot="{ row }">
            <el-avatar :size="40" :src="row.userAvatar" v-if="item.value == 'userAvatar'">
              {{ row.userAvatar ? row.nickname.charAt(0) : '' }}
            </el-avatar>
            <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange4"
          @current-change="handleCurrentChange4"
          :current-page="queryUser.pageNum"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="queryUser.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="userTotal"
        ></el-pagination>
      </div>
      <!-- <div class="table-footer">
        <span class="selected-count">接替人员：{{ selectedUserText }}</span>
      </div> -->

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeUserDialog">取消</el-button>
        <el-button type="primary" @click="confirmSelectUser">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getAppList, getGroupList, getGroupListByOwner, getGroupOwnerList, getUserListByApp, inheritGroup } from '@/api/toolView/groupInherit';
  export default {
    name: 'GroupInheritance',
    data() {
      return {
        appName: '',
        // 应用列表数据
        appLiat: [],
        // 查询参数
        queryOwner: {
          pageNum: 1, // 当前页码
          pageSize: 10, // 每页条数
          appName: '', // 应用名称
          userName: ''
        },
        ownerTotal: 0,
        // 选择客户群弹窗
        queryGroup: {
          pageNum: 1, // 当前页码
          pageSize: 10, // 每页条数
          appName: '', // 应用名称
          groupName: ''
        },
        groupTotal: 0,
        // 选择指定群主的客户群弹窗
        groupByOwnerQuery: {
          app: '',
          ownerIdList: [],
          groupName: '',
          pageNum: 1, // 当前页码
          pageSize: 10 // 每页条数
        },
        groupOwnerTotal: 0,
        groupByOwnerList: [],
        // 查询参数
        queryUser: {
          pageNum: 1, // 当前页码
          pageSize: 10, // 每页条数
          appName: '', // 应用名称
          userName: ''
        },
        userTotal: 0,
        // 群组列表数据
        groupList: [],
        // 选中的群组名称
        selectedGroup: '',
        // 选中的群组列表
        selectedGroupList: [],
        // 选中的群主列表
        selectedOwnerList: [],
        // 群组表格列配置
        groupTable: [
          {
            name: '客户群',
            value: 'groupName'
          },
          {
            name: '群主',
            value: 'owner'
          },
          {
            name: '创建时间',
            value: 'createTime'
          },
          {
            name: '群人数',
            value: 'membersCount'
          }
        ],
        // 接替人员表格列配置
        ownerTable: [
          {
            name: '头像',
            value: 'userAvatar'
          },
          {
            name: '姓名',
            value: 'nickname'
          },
          {
            name: '身份',
            value: 'roleName'
          }
        ],
        // 接替人员表格列配置
        userTable: [
          {
            name: '头像',
            value: 'userAvatar'
          },
          {
            name: '姓名',
            value: 'nickname'
          },
          {
            name: '身份',
            value: 'roleName'
          }
        ],
        // 接替人员列表数据
        ownerList: [],
        // 弹窗显示控制
        groupTypeDialogVisible: false, // 群组选择方式弹窗
        groupDialogVisible: false, // 群组选择弹窗
        groupByOwnerDialogVisible: false, // 接替群主的群聊选择弹窗
        ownerDialogVisible: false, // 接替群主选择弹窗
        userDialogVisible: false, // 接替人员选择弹窗
        // 搜索关键词
        groupSearchKeyword: '', // 群组搜索关键词
        staffSearchKeyword: '', // 接替人员搜索关键词
        userSearchKeyword: '', // 接替人员搜索关键词
        // 员工列表数据
        userList: [],
        // 已选择的接替人员数量
        selectedOwnerCount: 0,
        selectedUserCount: 0,
        selectedUserText: '',
        selectedUserList: {},
        chooseId: ''
      };
    },
    created() {
      // 页面创建时获取应用列表
      this.getAppListFn();
    },
    watch: {
      appName(e) {
        console.log('🚀🥶💩~ e', e);
        this.queryUser.appName = e;
        this.queryGroup.appName = e;
        this.queryOwner.appName = e;
        this.groupByOwnerQuery.app = e;

        this.getUserList();
      }
    },
    methods: {
      /**
       * 切换应用时触发
       * @param {string} e - 选中的应用值
       */
      changeApp(e) {
        this.selectedGroup = '';
        this.selectedUserText = '';
        this.selectedGroupList = [];
        this.selectedUserList = {};
        if (e) {
          // this.queryUser.appName = e;
          // this.queryGroup.appName = e;
          // this.queryOwner.appName = e;
          // this.groupByOwnerQuery.app = e;
          // this.getUserList();
        }
      },

      /**
       * 获取用户列表
       */
      async getUserList() {
        if (this.queryUser.appName) {
          let { data } = await getUserListByApp(this.queryUser);
          console.log(data, '============');
          this.userList = data.data;
          console.log('111111111🚀🥶💩~ this.userList', this.userList);
          this.userTotal = +data.totalCount;
        }
      },
      /**
       * 获取用户列表
       */
      async serchUserList() {
        if (this.queryUser.appName) {
          this.queryUser.pageNum = 1;
          await this.getUserList();
          this.$nextTick(() => {
            this.userList.forEach((row) => {
              if (this.chooseId.includes(row.id)) {
                this.$refs.singleSelectTable.toggleRowSelection(row, true);
              }
            });
          });
        }
      },
      /**
       * 获取群主列表
       */
      async getOwnerList() {
        if (!this.queryOwner.appName) {
          console.log('请先选择应用🚀🥶💩~ ============');
          this.$message.warning('请先选择应用');
          return;
        } else {
          let { data } = await getGroupOwnerList(this.queryOwner);
          console.log(data.data, '11111111111111111');
          this.ownerList = data.data;
          this.ownerTotal = +data.totalCount;
        }
      },
      serchOwner() {
        this.queryOwner.pageNum = 1;
        this.getOwnerList();
      },
      /**
       * 处理接替人员选择变化
       * @param {Array} selection - 选中的接替人员列表
       */
      handleOwnerChange(selection) {
        console.log(selection);
        this.selectedOwnerCount = selection.length;
        this.selectedOwnerList = selection;
        console.log('🚀🥶💩~ this.selectedOwnerList', this.selectedOwnerList);
      },
      /**
       * 处理接替人员选择变化
       * @param {Array} selection - 选中的接替人员列表
       */
      handleSelect(selection, row) {
        const table = this.$refs.singleSelectTable;
        // 清空之前所有选择
        table.clearSelection();
        // 只选当前行
        table.toggleRowSelection(row, true);
        // 记录当前选中的行
        this.selectedUserList = row;
        console.log(this.selectedUserList, selection, row, 'this.handleSelect');
        this.chooseId = row.id;
      },

      /**
       * 处理接替人员选择变化
       * @param {Array} selection - 选中的接替人员列表
       */
      closeUserDialog() {
        this.userDialogVisible = false;
        this.queryUser.userName = '';
        this.queryUser.pageNum = 1;
        this.getUserList();
      },
      /**
       * 根据关键词过滤群组名称
       */
      serchGroup() {
        this.queryGroup.pageNum = 1;
        this.getGroupListFn();
      },
      serchOwnerGroup() {
        this.groupByOwnerQuery.pageNum = 1;
        this.confirmOwner();
      },
      /**
       * 获取群组列表
       */
      async getGroupListFn() {
        console.log('🚀🥶请先选择应用💩~ 1231231231', 1231231231);
        if (!this.queryGroup.appName) return this.$message.warning('请先选择应用');
        let { data } = await getGroupList(this.queryGroup);
        console.log(data);
        this.groupList = data.data;
        console.log('🚀🥶💩~ this.groupList', this.groupList);
        this.groupTotal = +data.totalCount;
      },
      handleSizeChange1(val) {
        this.queryOwner.pageNum = 1;
        this.queryOwner.pageSize = val;
        this.getOwnerList();
      },
      handleCurrentChange1(val) {
        this.queryOwner.pageNum = val;
        this.getOwnerList();
      },
      handleSizeChange2(val) {
        this.queryGroup.pageNum = 1;
        this.queryGroup.pageSize = val;
        this.getGroupListFn();
      },
      handleCurrentChange2(val) {
        this.queryGroup.pageNum = val;
        this.getGroupListFn();
      },
      handleSizeChange3(val) {
        this.groupByOwnerQuery.pageNum = 1;
        this.groupByOwnerQuery.pageSize = val;
        this.confirmOwner();
      },
      handleCurrentChange3(val) {
        this.groupByOwnerQuery.pageNum = val;
        this.confirmOwner();
      },
      handleSizeChange4(val) {
        this.queryUser.pageNum = 1;
        this.queryUser.pageSize = val;
        this.getUserList();
      },
      handleCurrentChange4(val) {
        this.queryUser.pageNum = val;
        this.getUserList();
      },
      /**
       * 获取应用列表
       */
      async getAppListFn() {
        let { data } = await getAppList();
        this.appLiat = data;
      },

      /**
       * 显示群组选择方式弹窗
       */
      showSelectGroupTypeDialog() {
        if (this.appName) {
          this.groupTypeDialogVisible = true;
          this.getAppListFn();
        } else {
          console.log('123123🚀🥶💩~ this.appName', this.appName);

          return this.$message.warning('请先选择应用');
        }
      },
      showSelectGroupTypeDialog1() {
        if (this.appName) {
          this.selectedGroup = '';
          this.queryGroup = {
            pageNum: 1, // 当前页码
            pageSize: 10, // 每页条数
            appName: this.appName, // 应用名称 - 修改这里，使用当前选中的appName
            groupName: ''
          };
          this.groupTypeDialogVisible = true;
          this.getAppListFn();
        } else {
          console.log('请先选择应用🚀🥶💩~ this.appName', this.appName);
          return this.$message.warning('请先选择应用');
        }
      },
      /**
       * 显示群组选择方式弹窗
       */
      showSelectUserDialog() {
        if (this.appName) {
          this.userDialogVisible = true;
          this.$nextTick(() => {
            this.userList.forEach((row) => {
              if (this.chooseId.includes(row.id)) {
                this.$refs.singleSelectTable.toggleRowSelection(row, true);
              }
            });
          });

          this.getAppListFn();
        } else {
          console.log('请先选择应用🚀🥶💩~ this.appName', this.appName);
          return this.$message.warning('请先选择应用');
        }
      },
      showSelectUserDialog1() {
        if (this.appName) {
          this.selectedUserText = '';
          this.queryUser = {
            pageNum: 1, // 当前页码
            pageSize: 10, // 每页条数
            appName: this.appName, // 应用名称 - 修改这里，使用当前选中的appName
            userName: ''
          };
          this.userDialogVisible = true;
          // 重新选择后需要重新获取用户列表
          this.getUserList();
        } else {
          console.log('12🚀🥶💩~ this.appName', this.appName);
          return this.$message.warning('请先选择应用');
        }
      },
      /**
       * 显示群组选择弹窗
       */
      async showSelectGroupDialog() {
        this.groupTypeDialogVisible = false;
        console.log('🚀🥶💩~ 测试');
        console.log('🚀🥶💩~ this.appName', this.appName);
        this.queryGroup.appName = this.appName;
        await this.getGroupListFn();
        this.groupDialogVisible = true;
      },

      /**
       * 显示接替人员选择弹窗
       */
      showSelectStaffDialog() {
        this.ownerDialogVisible = true;
      },

      /**
       * 处理按群主选择群组
       */
      async handleSelectStaff() {
        this.groupTypeDialogVisible = false;
        await this.getOwnerList();
        this.showSelectStaffDialog();
      },

      /**
       * 处理群组选择变化
       * @param {Array} selection - 选中的群组列表
       */
      handleGroupSelectionChange(selection) {
        console.log(selection);
        this.selectedGroupList = selection;
      },
      /**
       * 处理群组选择变化
       * @param {Array} selection - 选中的群组列表
       */
      handleOwnerGroupSelectionChange(selection) {
        console.log(selection);
        this.selectedGroupList = selection;
      },

      /**
       * 确认选择群组
       */
      confirmSelectGroup() {
        let text = '';
        if (this.selectedGroupList.length > 0) {
          if (this.selectedGroupList.length == 1) {
            text = this.selectedGroupList[0].groupName;
          } else {
            text = `【${this.selectedGroupList[0].groupName}】等${this.selectedGroupList.length}个群聊`;
          }
        } else {
          this.$message.warning('请选择群组');
        }
        this.selectedGroup = text;
        this.groupDialogVisible = false;
      },
      confirmOwnerGroup() {
        let text = '';
        if (this.selectedGroupList.length > 0) {
          if (this.selectedGroupList.length == 1) {
            text = this.selectedGroupList[0].groupName;
          } else {
            text = `【${this.selectedGroupList[0].groupName}】等${this.selectedGroupList.length}个群聊`;
          }
        } else {
          this.$message.warning('请选择群组');
        }
        this.selectedGroup = text;
        this.groupByOwnerDialogVisible = false;
      },
      closeOwnerGroup() {
        this.selectedGroupList = [];
        this.groupByOwnerDialogVisible = false;
      },
      closeGroupDialog() {
        this.selectedGroupList = [];
        this.groupDialogVisible = false;
      },
      /**
       * 确认选择接替人员
       */
      async confirmOwner() {
        if (this.selectedOwnerList.length > 0) {
          let ownerIdList = this.selectedOwnerList.map((i) => i.tencentId);
          console.log(ownerIdList, '====================');
          this.groupByOwnerQuery.ownerIdList = ownerIdList;
          // this.groupByOwnerQuery.ownerIdList = JSON.stringify(ownerIdList);
          let { data } = await getGroupListByOwner(this.groupByOwnerQuery);
          if (data.totalCount != 0) {
            console.log(data);
            this.groupByOwnerList = data.data;
            this.groupOwnerTotal = +data.totalCount;
            this.ownerDialogVisible = false;
            this.groupByOwnerDialogVisible = true;
          } else {
            this.$message.warning('该群主没有群组');
          }
        } else {
          this.$message.warning('请选择群主');
        }
      },
      closeOwnerDialog() {
        this.ownerDialogVisible = false;
        this.queryOwner = {
          pageNum: 1, // 当前页码
          pageSize: 10, // 每页条数
          appName: '', // 应用名称
          userName: ''
        };
      },
      /**
       * 确认选择接替人员
       */
      confirmSelectUser() {
        if (this.selectedUserList && this.selectedUserList.id) {
          this.selectedUserText = this.selectedUserList.nickname;
          this.userDialogVisible = false;
        } else {
          this.$message.warning('请选择接替人员');
        }
      },

      /**
       * 提交表单
       */
      handleSubmit() {
        console.log(this.selectedGroupList);
        let obj = {
          groupIdList: this.selectedGroupList.map((i) => i.groupId),
          userId: this.selectedUserList.tencentId
        };
        console.log(obj);
        if (this.selectedGroupList.length < 1) {
          return this.$message.warning('请选择群组');
        }
        if (!this.selectedUserList.tencentId) {
          return this.$message.warning('请选择接替人员');
        }
        // return;
        // TODO: 实现提交逻辑
        this.$confirm('确认继承该群组?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            this.$loading({
              lock: true,
              text: '继承中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            console.log('🚀🥶💩~ obj', obj);
            await inheritGroup(obj);
            this.$message.success('继承成功');
            this.appName = '';
            this.selectedGroup = '';
            this.selectedUserText = '';
            this.selectedGroupList = [];
            this.selectedUserList = {};
            this.$forceUpdate();
          })
          .catch(() => {
            this.$loading().close();
            // this.$message.info('已取消');
          })
          .finally(() => {
            this.$loading().close();
          });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .group-inheritance {
    padding: 20px;

    .box-card {
      .content {
        .description {
          margin-bottom: 20px;
          color: #606266;
          font-size: 14px;

          .notice {
            margin-top: 10px;
            color: #909399;
            font-size: 12px;
          }
        }

        .form-section {
          .form-item {
            margin-bottom: 20px;

            .label {
              margin-bottom: 10px;
              font-weight: bold;
              color: #303133;
            }

            .group-select,
            .staff-select {
              .selected-group,
              .selected-staff {
                margin-top: 10px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 10px;
                background-color: #f5f7fa;
                border-radius: 4px;
              }

              .refresh-btn {
                color: #409eff;
              }
            }
          }

          .form-actions {
            margin-top: 30px;
            text-align: center;
          }
        }
      }
    }
  }

  // 群组类型选择弹窗样式
  .group-type-dialog {
    .group-type-container {
      display: flex;
      justify-content: space-between;
      padding: 20px;

      .group-type-item {
        width: 45%;
        height: 120px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          background-color: #f5f7fa;
        }

        .type-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .type-desc {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }

  // 选择群组弹窗样式
  .select-group-dialog {
    .search-bar {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .upload-area {
        display: flex;
        align-items: center;
        color: #909399;

        .el-button {
          margin-left: 10px;
        }
      }
    }

    .group-tree {
      // max-height: 400px;
      // overflow-y: auto;

      .custom-tree-node {
        display: flex;
        align-items: center;

        .node-icon {
          margin-right: 8px;
        }
      }
    }
  }

  // 选择员工弹窗样式
  .select-staff-dialog {
    .search-bar {
      margin-bottom: 20px;
    }

    .table-footer {
      margin-top: 15px;
      padding: 10px 0;

      .selected-count {
        color: #606266;
        font-size: 14px;
      }
    }
  }
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
</style>
