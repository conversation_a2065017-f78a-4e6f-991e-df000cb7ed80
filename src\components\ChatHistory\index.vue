<template>
  <div class="chat-history-container">
    <!-- 搜索和筛选区域 -->

    <div class="search-filter-section">
      <div v-if="activeType == 'all'" class="search-box">
        <el-input v-model.trim="searchKey" clearable placeholder="搜索聊天记录" prefix-icon="el-icon-search" @change="handleSearch">
          <template v-if="searchResults.total > 0" #append>
            <div class="search-results">
              <span>{{ searchResults.total }}条结果</span>
            </div>
          </template>
        </el-input>
      </div>
      <div v-else class="search-box">
        <el-input v-model.trim="searchKey" clearable placeholder="搜索文件名称" prefix-icon="el-icon-search" style="width: 300px" @change="handleSearch"></el-input>
      </div>
      <div class="filter-box">
        <el-select v-if="activeType != 'all'" v-model="filterType" class="sender-select" clearable placeholder="文件类型" style="width: 150px" @change="filterTypeFn">
          <el-option v-for="(sender, index) in fileList" :key="index" :label="sender.name" :value="sender.value"></el-option>
        </el-select>
        <!--        v-el-select-loadmore="handleLoadmore"-->
        <el-select v-model="filterSender" class="sender-select" clearable placeholder="发送人" @change="filterSenderFn">
          <el-option v-for="(sender, index) in userList" :key="index" :label="sender.nickname" :value="sender.tencentId"></el-option>
        </el-select>
        <el-date-picker v-model="filterDate" class="date-picker" placeholder="选择日期" type="date" @change="handleDateChange"></el-date-picker>
      </div>
    </div>

    <!-- 聊天记录列表 -->
    <div v-if="messageList.length > 0" style="height: calc(100vh - 270px)">
      <div ref="chatList" v-infinite-scroll="loadmore" :infinite-scroll-distance="distance" class="chat-list">
        <div v-for="(message, index) in messageList" :key="index" class="message-group">
          <!-- 日期分组 -->
          <div v-if="shouldShowDate(message, index)" class="date-divider">
            {{ formatTime(message.createTime) }}
          </div>

          <!-- 消息内容 -->
          <div :class="['message-item', message.senderId == self.tencentId ? 'self' : 'other', shouldHighlight(index) ? 'highlight' : '']">
            <div class="message-header">
              <span :class="['sender-name', message.senderId == self.tencentId ? 'self' : 'other']">{{ message.nickName }}</span>
              <span class="message-time">{{ formatMessageTime(message.createTime) }}</span>
              <span v-if="message.senderRoleName" class="message-tag" type="success">{{ message.senderRoleName }}</span>
              <el-button v-if="message.messageType === 'TIMRelayElem'" class="collapse-btn" type="text" @click="toggleMessageCollapse(message)">
                <i :class="['el-icon', isMessageCollapsed(message) ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"></i>
              </el-button>
            </div>

            <div
              :class="[
                message.messageType != 'TIMCustomElem' ? 'message-content' : '',
                message.messageType == 'TIMImageElem' ? 'image' : message.messageType == 'TIMFileElem' ? 'file' : '',
                { highlight: shouldHighlight(index) }
              ]"
            >
              <!-- 文本消息 -->
              <div v-if="message.messageType == 'TIMTextElem'">
                <!-- <span>{{ message.messageContent }}</span> -->
                <!-- <img
                v-if="message.messageContent.includes('TUIEmoji')"
                :src="getFaceUrl(message.messageContent)"
                :alt="message.messageContent"
                class="face-image"
                style="width: 20px; height: 20px"
              /> -->
                <div v-if="message.messageContent.includes('TUIEmoji')">
                  <img v-for="(i, o) in getFaceArr(message.messageContent)" :key="o" :alt="i" :src="getFaceUrl(i)" class="face-image" style="width: 20px; height: 20px" />
                </div>
                <span v-else>{{ message.messageContent }}</span>
              </div>

              <!-- 图片消息 -->
              <div v-if="message.messageType == 'TIMImageElem'">
                <div class="image-wrapper" @click="previewImage(message.messageContent)">
                  <img :src="message.messageContent" :style="getImageStyle(message.imageSize)" />
                </div>
              </div>

              <!-- 文件消息 -->
              <div v-if="message.messageType == 'TIMFileElem'">
                <div class="file-message" @click="handleFileClick(message)">
                  <div class="file-icon">
                    <i :class="getFileIconClass(message.fileType)"></i>
                  </div>
                  <div class="file-info">
                    <div class="file-name">{{ message.fileName }}</div>
                    <div class="file-size">{{ formatFileSize(message.fileSize) }}</div>
                  </div>
                </div>
              </div>

              <!-- 视频消息 -->
              <div v-if="message.messageType == 'TIMVideoFileElem'">
                <div class="video-wrapper">
                  <video :poster="formatVideo(message.messageContent, 'thumbUrl')" :src="formatVideo(message.messageContent, 'videoUrl')" controls preload="none"></video>
                </div>
              </div>

              <!-- 语音消息 -->
              <div v-if="message.messageType == 'TIMSoundElem'">
                <div class="audio-wrapper" @click="playAudio(message.messageContent)">
                  <i class="audio-icon"></i>
                  <span class="audio-duration">{{ formatDuration(message.second) || '' }}</span>
                </div>
              </div>

              <!-- 表情消息 -->
              <div v-if="message.messageType == 'TIMFaceElem'" class="face-message">
                <!-- <img :src="getFaceUrl(message.content.index)" :alt="message.content.data" class="face-image" /> -->
                <img :alt="message.messageContent" :src="getFaceUrl(message.messageContent)" class="face-image" />
              </div>

              <!-- 自定义消息 -->
              <div v-if="message.messageType == 'TIMCustomElem'">
                <div class="custom-content" v-html="formatCustomMessage(message)"></div>
              </div>

              <!-- 合并转发消息 -->
              <div v-if="message.messageType == 'TIMRelayElem' && message.messageContent" class="relay-message" @click="showRelayDetailDialog(message)">
                <div class="relay-header">
                  <span class="relay-title">聊天记录</span>
                  <span class="relay-count">{{ message.messageContent && message.messageContent.details.length }}条消息</span>
                </div>
                <div class="relay-preview">
                  <div v-for="(msg, idx) in message.messageContent.content.slice(0, 3)" :key="idx" class="relay-item">
                    <span class="relay-content">{{ msg }}</span>
                  </div>
                  <div v-if="message.messageContent.content.length > 3" class="relay-more">查看更多消息</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <NoMore v-else text="暂无会话内容"></NoMore>
    <!-- 图片预览弹窗 -->
    <el-dialog :visible.sync="showImagePreview" append-to-body custom-class="image-preview-dialog" title="图片预览" top="1vh" width="30%">
      <div style="display: flex; justify-content: center">
        <img :src="previewImageUrl" alt="preview" style="width: 50%" />
      </div>
    </el-dialog>

    <!-- 转发详情弹窗 -->
    <el-dialog
      :before-close="handleRelayDetailClose"
      :title="currentRelayMessage ? currentRelayMessage.messageContent.title : '聊天记录'"
      :visible.sync="showRelayDetail"
      custom-class="relay-detail-dialog"
      width="45%"
    >
      <div v-if="currentRelayMessage" class="relay-detail-container">
        <div class="relay-detail-header">
          <div class="relay-detail-count">{{ currentRelayMessage.messageContent.details.length }}条消息</div>
        </div>
        <div class="relay-detail-list">
          <div v-for="(msg, idx) in currentRelayMessage.messageContent.details" :key="idx" class="relay-detail-item">
            <div :class="['relay-detail-message', msg.senderId === self.tencentId ? 'self' : 'other']">
              <div class="relay-detail-sender">
                <img :src="msg.userAvatar" class="user-avatar" />
                <span :class="['sender-name', msg.senderId === self.tencentId ? 'self' : 'other']">{{ msg.nickName }}</span>
                <span v-if="msg.roleName" class="role-tag" size="mini" type="success">{{ msg.roleName }}</span>
              </div>
              <div class="relay-detail-content">
                <!-- 文本消息 -->
                <div v-if="msg.messageType === 'TIMTextElem'" class="text-message">
                  <div v-if="msg.content.includes('TUIEmoji')">
                    <img v-for="(i, o) in getFaceArr(msg.content)" :key="o" :alt="i" :src="getFaceUrl(i)" class="face-image" style="width: 20px; height: 20px" />
                  </div>
                  <span v-else>{{ msg.content }}</span>
                  <!-- {{ msg.content.includes('TUIEmoji') }} -->
                </div>
                <!-- 图片消息 -->
                <div v-if="msg.messageType === 'TIMImageElem'" class="image-message">
                  <img :src="msg.content" @click="previewImage(msg.content)" />
                </div>
                <!-- 文件消息 -->
                <div v-if="msg.messageType === 'TIMFileElem'" class="file-message" @click="downloadFile(msg)">
                  <div class="file-icon">
                    <i :class="getFileIconClass(msg.fileData.fileType)"></i>
                  </div>
                  <div class="file-info">
                    <div class="file-name">{{ msg.fileData.fileName }}</div>
                    <div class="file-size">{{ formatFileSize(msg.fileData.fileSize) }}</div>
                  </div>
                </div>
                <!-- 视频消息 -->
                <div v-if="msg.messageType == 'TIMVideoFileElem'">
                  <div class="video-wrapper">
                    <video :poster="msg.thumbUrl" :src="msg.videoUrl" controls preload="none"></video>
                  </div>
                </div>

                <!-- 语音消息 -->
                <div v-if="msg.messageType == 'TIMSoundElem'">
                  <div class="audio-wrapper" @click="playAudio(msg.content)">
                    <i class="audio-icon"></i>
                    <span class="audio-duration">{{ formatDuration(msg.second) || '' }}</span>
                  </div>
                </div>
                <!-- 表情消息 -->
                <div v-if="msg.messageType == 'TIMFaceElem'" class="face-message">
                  <img :alt="msg.content" :src="getFaceUrl(msg.content)" class="face-image" />
                </div>

                <!-- 自定义消息 -->
                <div v-if="msg.messageType == 'TIMCustomElem'" class="custom-message">
                  <div class="custom-content" v-html="formatCustomMessage(msg)"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 视频预览弹窗 -->
    <el-dialog :before-close="handleVideoClose" :visible.sync="showVideoPreview" custom-class="video-preview-dialog" title="视频预览" width="50%">
      <div v-if="currentVideo" class="video-preview-container">
        <video :src="currentVideo.fileUrl ? currentVideo.fileUrl : currentVideo.messageContent" autoplay class="video-player" controls></video>
        <div class="video-info">
          <div class="video-name">{{ currentVideo.fileName }}</div>
          <div class="video-size">{{ formatFileSize(currentVideo.fileSize) }}</div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加已确认人员弹窗 -->
    <el-dialog :visible.sync="showConfirmedUsers" custom-class="confirmed-users-dialog" title="已确认人员" width="30%">
      <div v-if="confirmedUsersList.length" class="confirmed-users-list">
        <div v-for="(user, index) in confirmedUsersList" :key="index" class="confirmed-user-item">
          <img :src="user.avatar" class="user-avatar" />
          <div class="user-info">
            <div class="user-name">{{ user.nickname }}</div>
            <div class="confirm-time">{{ formatMessageTime(user.confirmTime) }}</div>
          </div>
        </div>
      </div>
      <div v-else class="no-data">暂无确认人员</div>
    </el-dialog>
  </div>
</template>

<script>
  import { DEFAULT_BASIC_EMOJI_URL, DEFAULT_BASIC_EMOJI_URL_MAPPING } from './emoj';
  import { getCardUserList } from '@/api/chatContent/chatFile';
  import NoMore from '@/components/NoMore/index.vue';
  export default {
    name: 'ChatHistory',
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    props: {
      // 消息列表数据
      messageList: {
        type: Array,
        default: () => []
      },
      activeType: {
        type: String,
        default: 'all'
      },
      // 用户列表数据
      userList: {
        type: Array,
        default: () => []
      },
      // 当前用户信息
      self: {
        type: Object,
        default: () => {}
      },
      distance: {
        type: Number,
        default: 0
      }
    },
    components: {
      NoMore
    },
    data() {
      return {
        // 图片预览弹窗显示状态
        showImagePreview: false,
        // 当前预览的图片URL
        previewImageUrl: '',
        // 搜索关键词
        searchKey: '',
        // 筛选的发送者ID
        filterSender: '',
        // 筛选的发送者ID
        filterType: '',
        // 筛选的日期
        filterDate: null,
        // 已折叠的消息ID集合
        collapsedMessages: new Set(),
        // 转发详情弹窗显示状态
        showRelayDetail: false,
        // 当前查看的转发消息
        currentRelayMessage: null,
        // 视频预览弹窗显示状态
        showVideoPreview: false,
        // 当前预览的视频信息
        currentVideo: null,
        // 日期选择器的快捷选项
        pickerOptions: {
          shortcuts: [
            {
              text: '最近一周',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '最近一个月',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setMonth(start.getMonth() - 1);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '最近三个月',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setMonth(start.getMonth() - 3);
                picker.$emit('pick', [start, end]);
              }
            }
          ]
        },
        // 添加查看确认人员弹窗相关数据
        showConfirmedUsers: false,
        confirmedUsersList: [],
        currentCardData: null,
        fileList: [
          { name: 'PDF', value: 'pdf' },
          { name: 'WORD', value: 'word' },
          { name: 'PPT', value: 'ppt' },
          { name: 'TXT', value: 'txt' },
          { name: 'XLSX', value: 'xlsx' },
          { name: 'MP3', value: 'mp3' },
          { name: 'MP4', value: 'mp4' },
          { name: 'ZIP', value: 'zip' },
          { name: 'DOC', value: 'doc' },
          { name: 'DOCX', value: 'docx' }
        ],
        highlightedMessages: new Set(), // 存储需要高亮的消息索引
        searchResults: {
          total: 0,
          current: -1
        },
        loading: false
      };
    },
    computed: {},
    mounted() {
      this.initGlobalHandlers();
    },
    beforeDestroy() {
      // 清理全局方法
      window.viewConfirmedUsers = undefined;
    },
    methods: {
      // 下拉加载
      handleLoadmore() {
        if (!this.loading) {
          // if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          // this.selectObj.pageNum++;
          // this.getImUser();
        }
      },

      /**
       * 加载更多消息
       */
      loadmore() {
        console.log('loadmore', 111111);
        this.$emit('loadMore');
      },

      /**
       * 切换tab时取消高亮
       */
      deleteHlightedMessages() {
        // this.highlightedMessages.clear();
        this.searchResults.total = 0;
      },

      /**
       * 处理搜索输入
       */
      handleSearch() {
        // this.highlightedMessages.clear();

        if (!this.searchKey && !this.filterSender && !this.filterDate && !this.filterType) {
          this.emitFilterChange();
          console.log(1);
          return false;
        }
        console.log(2);
        // 遍历消息列表进行筛选和高亮
        this.messageList.forEach((message, index) => {
          let shouldHighlight = false;

          // 搜索关键词匹配
          if (this.searchKey) {
            if (message.messageType === 'TIMTextElem' && message.messageContent.toLowerCase().includes(this.searchKey.toLowerCase())) {
              shouldHighlight = true;
            } else if (message.messageType === 'TIMFileElem' && message.fileName.toLowerCase().includes(this.searchKey.toLowerCase())) {
              shouldHighlight = true;
            }
          }

          // 发送人筛选
          if (this.filterSender && message.senderId !== this.filterSender) {
            shouldHighlight = false;
          }

          // 日期筛选
          if (this.filterDate) {
            const messageDate = new Date(message.createTime).toDateString();
            const filterDate = new Date(this.filterDate).toDateString();
            if (messageDate !== filterDate) {
              shouldHighlight = false;
            }
          }

          // 文件类型筛选
          if (this.filterType && message.fileType !== this.filterType) {
            shouldHighlight = false;
          }

          // if (shouldHighlight) {
          //   this.highlightedMessages.add(index);
          // }
        });
        console.log(this.highlightedMessages, '条结果');
        // 更新搜索结果计数
        // this.searchResults.total = this.highlightedMessages.size;
        // this.searchResults.current = this.highlightedMessages.size > 0 ? 0 : -1;
        console.log(3);

        // 触发筛选事件
        this.emitFilterChange();

        // 滚动到第一个高亮消息
        // if (this.highlightedMessages.size > 0) {
        //   this.$nextTick(() => {
        //     this.scrollToHighlighted(Array.from(this.highlightedMessages)[0]);
        //   });
        // }
      },

      /**
       * 处理发送者筛选
       */
      filterSenderFn() {
        this.handleSearch();
      },
      filterTypeFn() {
        this.handleSearch();
      },

      /**
       * 处理日期筛选
       */
      handleDateChange() {
        this.handleSearch();
      },

      /**
       * 发送筛选条件变更事件
       */
      emitFilterChange() {
        let filterParams = {};
        if (this.activeType == 'all') {
          filterParams = {
            content: this.searchKey,
            senderId: this.filterSender,
            times: this.filterDate ? this.formatDate(this.filterDate) : null
          };
        } else {
          filterParams = {
            content: this.searchKey,
            senderId: this.filterSender,
            times: this.filterDate ? this.formatDate(this.filterDate) : null,
            fileType: this.filterType
          };
        }
        // console.log('472🚀🥶💩~ filterParams', filterParams);
        this.$emit('filterChange', filterParams);
      },

      /**
       * 格式化日期为 YYYY-MM-DD 格式
       * @param {Date} date - 日期对象
       * @returns {string} 格式化后的日期字符串
       */
      formatDate(date) {
        const d = new Date(date);
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
      },

      /**
       * 跳转到下一个搜索结果
       */
      jumpToNext() {
        if (!this.searchKey) return;
        this.$emit('jump-to-next', this.searchKey);
      },

      /**
       * 判断是否应该显示日期分隔线
       * @param {Object} message - 当前消息
       * @param {number} index - 消息索引
       * @returns {boolean} 是否显示日期分隔线
       */
      shouldShowDate(message, index) {
        if (index === 0) return true;
        const prevMessage = this.messageList[index - 1];
        return !this.isSameDay(message.createTime, prevMessage.createTime);
      },

      /**
       * 判断两个日期是否是同一天
       * @param {string|number} date1 - 第一个日期
       * @param {string|number} date2 - 第二个日期
       * @returns {boolean} 是否是同一天
       */
      isSameDay(date1, date2) {
        const d1 = new Date(date1);
        const d2 = new Date(date2);
        return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
      },

      /**
       * 格式化日期为 YYYY-MM-DD 格式
       * @param {string|number} time - 时间戳
       * @returns {string} 格式化后的日期字符串
       */
      formatTime(time) {
        const date = new Date(time);
        return date.toLocaleDateString().replaceAll('/', '-');
      },

      /**
       * 格式化消息时间为 HH:mm 格式
       * @param {string|number} timestamp - 时间戳
       * @returns {string} 格式化后的时间字符串
       */
      formatMessageTime(timestamp) {
        const date = new Date(timestamp);
        return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      },

      /**
       * 根据文件名获取文件图标类名
       * @param {string} fileType - 文件类型
       * @returns {string} 图标类名
       */
      getFileIconClass(fileType) {
        const iconMap = {
          pdf: 'el-icon-document',
          doc: 'el-icon-document',
          docx: 'el-icon-document',
          xls: 'el-icon-document',
          xlsx: 'el-icon-document',
          txt: 'el-icon-document-text',
          mp4: 'el-icon-video-camera',
          mov: 'el-icon-video-camera',
          avi: 'el-icon-video-camera',
          wmv: 'el-icon-video-camera',
          mp3: 'el-icon-headset',
          wav: 'el-icon-headset',
          jpg: 'el-icon-picture',
          jpeg: 'el-icon-picture',
          png: 'el-icon-picture',
          gif: 'el-icon-picture',
          zip: 'el-icon-folder',
          rar: 'el-icon-folder',
          '7z': 'el-icon-folder'
        };
        return iconMap[fileType?.toLowerCase()] || 'el-icon-document';
      },

      /**
       * 格式化文件大小
       * @param {number} bytes - 文件大小（字节）
       * @returns {string} 格式化后的文件大小
       */
      formatFileSize(bytes) {
        if (bytes == 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      },

      /**
       * 获取图片样式
       * @param {Object} size - 图片尺寸对象
       * @returns {Object} 样式对象
       */
      getImageStyle(size) {
        if (!size) return {};
        const maxWidth = 300;
        const maxHeight = 200;
        let width = size.width;
        let height = size.height;

        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }

        return {
          width: width + 'px',
          height: height + 'px'
        };
      },

      /**
       * 格式化时长
       * @param {number} seconds - 秒数
       * @returns {string} 格式化后的时长
       */
      formatDuration(seconds) {
        if (seconds) {
          const minutes = Math.floor(seconds / 60);
          const remainingSeconds = Math.floor(seconds % 60);
          return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        } else {
          return '0:00';
        }
      },

      /**
       * 播放音频
       * @param {Object} audio - 音频信息
       */
      playAudio(url) {
        // if (this.audioPlayer) {
        //   this.audioPlayer.pause();
        // }

        // this.audioPlayer = new Audio(audio.url);
        // this.audioPlayer.play();
        const audio = new Audio(url);
        audio
          .play()
          .then(() => {
            // 播放成功后再做后续处理
            // setTimeout(() => {
            //   audio.pause();
            // }, 500); // 至少播放 0.5 秒
          })
          .catch((err) => {
            console.error('播放失败：', err);
          });
      },

      /**
       * 预览图片
       * @param {string} url - 图片URL
       */
      previewImage(url) {
        this.previewImageUrl = url;
        this.showImagePreview = true;
      },

      /**
       * 下载文件
       * @param {Object} file - 文件信息
       */
      downloadFile(file) {
        console.log(file);
        // return;
        if (file.fileData) {
          if (file.fileData.fileType == 'mp4') {
            this.currentVideo = file.fileData;
            this.showVideoPreview = true;
          } else {
            window.open(file.fileData.fileUrl, '_blank');
          }
        } else {
          if (file.fileType == 'mp4') {
            this.currentVideo = file.fileData;
            this.showVideoPreview = true;
          } else {
            if (file.messageContent) {
              let arr = file.messageContent.split(';');
              if (arr.length > 1) {
                window.open(arr[1], '_blank');
              } else {
                window.open(file.messageContent, '_blank');
              }
              // window.open(file.messageContent, '_blank');
            }
          }
        }
      },
      getFaceArr(content) {
        const result = content.match(/\[([^\]]+)\]/g)?.map((item) => item.slice(1, -1)) || [];
        // console.log(result);
        return result;
      },
      /**
       * 获取表情图片URL
       * @param {number} index - 表情索引
       * @returns {string} 表情图片URL
       */
      getFaceUrl(index) {
        // return `表情`;
        let str = `[${index}]`;
        // return `/static/faces/${index}.png`;
        return DEFAULT_BASIC_EMOJI_URL + DEFAULT_BASIC_EMOJI_URL_MAPPING[str];
      },
      /**
       * 解析文件内容字符串为对象
       * @param {string} content - 文件内容字符串
       * @returns {Object} 解析后的文件信息对象
       */
      parseKeyValueString(str) {
        // 去掉首尾的大括号
        const cleanStr = str.trim().replace(/^{|}$/g, '');
        const regex = /(\w+)=((?:https?:\/\/)?[^,{}]+)(?:,|$)/g;

        const result = {};
        let match;

        while ((match = regex.exec(cleanStr)) !== null) {
          const key = match[1].trim();
          const value = match[2].trim();

          // 如果是数字字符串，尝试转为数字
          result[key] = /^\d+$/.test(value) ? Number(value) : value;
        }

        return result;
      },
      /**
       * 解析文件内容字符串为对象
       * @param {string} content - 文件内容字符串
       * @returns {Object} 解析后的文件信息对象
       */
      parseFileContent(content) {
        try {
          // 将字符串转换为JSON对象
          const str = content.replace(/{/g, '{"').replace(/=/g, '":"').replace(/, /g, '","').replace(/}/g, '"}');
          return JSON.parse(str);
        } catch (e) {
          console.error('解析文件内容失败:', e);
          return {
            fileName: '未知文件',
            fileSize: 0,
            fileUrl: '',
            fileType: ''
          };
        }
      },

      /**
       * 显示转发详情弹窗
       * @param {Object} message - 转发消息
       */
      showRelayDetailDialog(message) {
        console.log(message, '111111111111111');
        let newData = JSON.parse(JSON.stringify(message));
        newData.messageContent.details.forEach((item) => {
          if (item.messageType == 'TIMVideoFileElem') {
            let str = item.content;
            // 正则提取 videoUrl 和 thumbUrl
            const videoMatch = str.match(/videoUrl=([^,]+),/);
            const thumbMatch = str.match(/thumbUrl=(.+)}/);

            const result = {
              videoUrl: videoMatch ? videoMatch[1] : '',
              thumbUrl: thumbMatch ? thumbMatch[1] : ''
            };
            item.videoUrl = result.videoUrl;
            item.thumbUrl = result.thumbUrl;
          }
          if (item.messageType == 'TIMFileElem') {
            let obj = this.parseKeyValueString(item.content);
            // console.log(obj);
            item.fileData = obj;
          }
        });
        console.log(newData, '=====');
        this.currentRelayMessage = newData;
        this.showRelayDetail = true;
      },

      /**
       * 关闭转发详情弹窗
       */
      handleRelayDetailClose() {
        this.showRelayDetail = false;
        this.currentRelayMessage = null;
      },

      /**
       * 处理文件点击事件
       * @param {Object} message - 文件消息
       */
      handleFileClick(message) {
        console.log(message, '1111111111111');

        // const fileInfo = this.parseFileContent(message.messageContent);
        const fileInfo = message;
        if (fileInfo.fileType && ['mp4', 'mov', 'avi', 'wmv'].includes(fileInfo.fileType.toLowerCase())) {
          this.currentVideo = fileInfo;
          this.showVideoPreview = true;
        } else {
          this.downloadFile(fileInfo);
        }
      },

      /**
       * 格式化自定义消息
       * @param {string|Object} content - 消息内容
       * @returns {string} 格式化后的消息内容
       */
      formatCustomMessage(message) {
        try {
          const data = typeof message.messageContent === 'string' ? JSON.parse(message.messageContent) : message.messageContent;
          if (message.senderId.includes('@RBT')) {
            console.log(data, '\\\\\\\\\\\\\\\\\\');
            return this.formatCardMessage(message, data);
          } else {
            // 处理纯文本消息，创建简洁的文本卡片
            const textContent = data || message.messageContent || '自定义消息';
            return this.formatTextMessage(textContent);
          }
        } catch (e) {
          // 如果解析失败，可能是纯文本消息
          return this.formatTextMessage(message.messageContent || '自定义消息');
        }
      },

      /**
       * 格式化纯文本消息
       * @param {string} textContent - 文本内容
       * @returns {string} 格式化后的HTML字符串
       */
      formatTextMessage(textContent) {
        // 处理表情符号
        const processedText = textContent.replace(/\[([^\]]+)\]/g, (match, emoji) => {
          return `<span class="emoji">${emoji}</span>`;
        });

        return `
          <div class="custom-card-message text-message-card">
            <div class="card-content">
              <div class="text-content">${processedText}</div>
            </div>
          </div>
        `;
      },

      /**
       * 格式化课程确认卡片
       * @param {Object} data - 课程确认数据
       * @returns {string} 格式化后的HTML字符串
       */
      formatClassConfirmCard(data) {
        // 解析课程信息 - 修复换行符分割问题
        const lines = data.content.split(/\\n|\n/);
        const courseInfo = {};

        console.log('课程确认消息内容分割结果:', lines); // 调试日志

        lines.forEach((line) => {
          if (line.includes('学生姓名：')) {
            courseInfo.studentName = line.replace('学生姓名：', '').trim();
          } else if (line.includes('课程类型：')) {
            courseInfo.courseType = line.replace('课程类型：', '').trim();
          } else if (line.includes('词库：')) {
            courseInfo.vocabulary = line.replace('词库：', '').trim();
          } else if (line.includes('试课时间：')) {
            courseInfo.classTime = line.replace('试课时间：', '').trim();
          } else if (line.includes('试课教练：')) {
            courseInfo.teacher = line.replace('试课教练：', '').trim();
          }
        });

        console.log('解析后的课程确认信息:', courseInfo); // 调试日志

        return `
          <div class="custom-card-message class-confirm-card">
            <div class="card-header">
              <div class="card-icon">
                <i class="el-icon-s-check"></i>
              </div>
              <div class="card-title">${data.title}</div>
            </div>
            <div class="card-content">
              <div class="course-info">
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-user"></i>
                    <span>学生姓名</span>
                  </div>
                  <div class="info-value">${courseInfo.studentName || '未知'}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-reading"></i>
                    <span>课程类型</span>
                  </div>
                  <div class="info-value">${courseInfo.courseType || '未知'}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-collection"></i>
                    <span>词库</span>
                  </div>
                  <div class="info-value">${courseInfo.vocabulary || '未知'}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-time"></i>
                    <span>试课时间</span>
                  </div>
                  <div class="info-value">${courseInfo.classTime || '未知'}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-user-solid"></i>
                    <span>试课教练</span>
                  </div>
                  <div class="info-value">${courseInfo.teacher || '未知'}</div>
                </div>
              </div>
              <div class="confirm-actions">
                <div class="confirm-note">
                  <i class="el-icon-warning"></i>
                  <span>请确认以上信息是否正确</span>
                </div>
              </div>
            </div>
          </div>
        `;
      },

      /**
       * 格式化派单卡片
       * @param {Object} data - 派单数据
       * @returns {string} 格式化后的HTML字符串
       */
      formatDispatchCard(data) {
        // 解析课程信息 - 修复换行符分割问题
        const lines = data.content.split(/\\n|\n/);
        const courseInfo = {};

        console.log('派单消息内容分割结果:', lines); // 调试日志

        lines.forEach((line) => {
          if (line.includes('课程名称：')) {
            courseInfo.courseName = line.replace('课程名称：', '').trim();
          } else if (line.includes('课程类型：')) {
            courseInfo.courseType = line.replace('课程类型：', '').trim();
          } else if (line.includes('学生姓名：')) {
            courseInfo.studentName = line.replace('学生姓名：', '').trim();
          } else if (line.includes('日期：')) {
            courseInfo.date = line.replace('日期：', '').trim();
          } else if (line.includes('时间：')) {
            courseInfo.time = line.replace('时间：', '').trim();
          } else if (line.includes('可接单时间：')) {
            courseInfo.acceptTime = line.replace('可接单时间：', '').trim();
          } else if (line.includes('备注：')) {
            courseInfo.remark = line.replace('备注：', '').trim();
          }
        });

        console.log('解析后的课程信息:', courseInfo); // 调试日志

        // 解析URL参数
        const urlParams = this.parseUrlParams(data.url);
        const orderId = urlParams.id || '未知';
        const deliverMerchant = urlParams.deliverMerchant || '未知';

        return `
          <div class="custom-card-message dispatch-order-card">
            <div class="card-header">
              <div class="card-icon">
                <i class="el-icon-s-order"></i>
              </div>
              <div class="card-title">${data.title}</div>
            </div>
            <div class="card-content">
              <div class="course-info">
                <div class="info-section">
                  <div class="section-title">
                    <i class="el-icon-reading"></i>
                    <span>课程信息</span>
                  </div>
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="info-label">课程名称</span>
                      <span class="info-value">${courseInfo.courseName || '未知'}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">课程类型</span>
                      <span class="info-value">${courseInfo.courseType || '未知'}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">学生姓名</span>
                      <span class="info-value">${courseInfo.studentName || '未知'}</span>
                    </div>
                  </div>
                </div>

                <div class="info-section">
                  <div class="section-title">
                    <i class="el-icon-time"></i>
                    <span>时间安排</span>
                  </div>
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="info-label">上课日期</span>
                      <span class="info-value">${courseInfo.date || '未知'}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">上课时间</span>
                      <span class="info-value">${courseInfo.time || '未知'}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">可接单时间</span>
                      <span class="info-value">${courseInfo.acceptTime || '未知'}</span>
                    </div>
                  </div>
                </div>

                <div class="info-section">
                  <div class="section-title">
                    <i class="el-icon-info"></i>
                    <span>订单信息</span>
                  </div>
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="info-label">订单ID</span>
                      <span class="info-value order-id">${orderId}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">交付商户</span>
                      <span class="info-value merchant-id">${deliverMerchant}</span>
                    </div>
                  </div>
                </div>

                ${
                  courseInfo.remark
                    ? `
                <div class="info-section">
                  <div class="section-title">
                    <i class="el-icon-chat-line-square"></i>
                    <span>备注信息</span>
                  </div>
                  <div class="remark-content">
                    ${courseInfo.remark}
                  </div>
                </div>
                `
                    : ''
                }
              </div>
            </div>
          </div>
        `;
      },

      /**
       * 解析URL参数
       * @param {string} url - URL字符串
       * @returns {Object} 参数对象
       */
      parseUrlParams(url) {
        const params = {};
        if (url.includes('?')) {
          const queryString = url.split('?')[1];
          const pairs = queryString.split('&');
          pairs.forEach((pair) => {
            const [key, value] = pair.split('=');
            if (key && value) {
              params[key] = decodeURIComponent(value);
            }
          });
        }
        return params;
      },

      /**
       * 格式化卡片消息
       * @param {Object} data - 卡片消息数据
       * @returns {string} 格式化后的HTML字符串
       */
      formatCardMessage(message, data) {
        if (data && data.bodyType) {
          switch (data.bodyType) {
            case '上课确认卡片':
              return `
            <div class="custom-card-message confirm-card">
              <div class="card-header">
                <div class="card-title">${data.title}</div>
              </div>
              <div class="card-content">
                <div class="card-confirm">
                  <div class="confirm-text">${data.content}</div>
                </div>
              </div>
            </div>
          `;
            case 'CLASS_CONFIRM':
              return this.formatClassConfirmCard(data);
            case '链接消息':
              return `
          <div class="custom-card-message link-card">
              <div class="card-header">
                <div class="card-title">${data.title}</div>
              </div>
              <div class="card-content">
                <div class="card-teacher">
                  <span class="label">${data.content}</span>
                </div>
                <div class="card-image">
                  <img src="${data.img}" alt="链接预览图" style="width: 150px;height:150px"></img>
                </div>
                <div class="card-link">
                  <a href="${data.url}" target="_blank">
                    <i class="el-icon-link"></i>
                    <span>${data.url}</span>
                  </a>
                </div>
              </div>
            </div>
          `;
            //       case '主交付中心派单':
            //       return `
            // <div class="custom-card-message default-card">
            //           <div class="card-header">
            //             <div class="card-title">${data.title}</div>
            //             <div class="card-subtitle">课程信息</div>
            //           </div>
            //           <div class="card-content">
            //             <div class="card-time">
            //               <i class="el-icon-date"></i>
            //               <span>${data.day} ${data.week}</span>
            //             </div>
            //             <div class="card-time">
            //               <i class="el-icon-time"></i>
            //               <span>${data.time}</span>
            //             </div>
            //             <div class="card-teacher">
            //               <i class="el-icon-user"></i>
            //               <span class="label">上课人：</span>
            //             </div>
            //             <div class="card-confirm">
            //               <div class="confirm-text">
            //                 <i class="el-icon-bell"></i>
            //                 <span>请${data.sureRole.join(',')}及时确认~</span>
            //               </div>
            //               <div class="confirm-buttons">
            //                 <button class="view-btn" onclick="window.viewConfirmedUsers('${encodeURIComponent(JSON.stringify(message))}')">
            //                   <i class="el-icon-view"></i>
            //                   <span>查看人员</span>
            //                 </button>
            //               </div>
            //             </div>
            //           </div>
            //         </div>
            //         `;
            default:
              return `
            <div class="custom-card-message dispatch-card">
              <div class="card-header">
                <div class="card-title">${data.title ?? ''}</div>
              </div>
              <div class="card-content">
                <div class="card-confirm">
                  <div class="confirm-text">${data.content.replace(/\n/g, '<br>')}</div>
                </div>
              </div>
            </div>
            `;
          }
        } else {
          // 处理有title和url的消息
          if (data && data.title && data.url) {
            // 判断是否为派单消息（有content字段且包含特定关键词）
            if (data.content && (data.title.includes('派单') || data.content.includes('课程名称') || data.content.includes('可接单时间'))) {
              return this.formatDispatchCard(data);
            }
            // 处理报告消息类型（只有title和url，没有content或content不包含派单关键词）
            else {
              return `
                <div class="custom-card-message report-card">
                  <div class="card-header">
                    <div class="card-icon">
                      <i class="el-icon-document"></i>
                    </div>
                    <div class="card-title">${data.title}</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description">
                      <p>点击下方按钮查看详细报告</p>
                    </div>
                    <div class="card-link">
                      <a href="${data.url}" target="_blank" class="report-btn">
                        <i class="el-icon-view"></i>
                        <span>查看报告</span>
                      </a>
                    </div>
                  </div>
                </div>
              `;
            }
          }
        }

        // 如果没有匹配到任何类型，返回默认显示
        return `
          <div class="custom-card-message default-card">
            <div class="card-header">
              <div class="card-title">自定义消息</div>
            </div>
            <div class="card-content">
              <div class="card-confirm">
                <div class="confirm-text">${JSON.stringify(data)}</div>
              </div>
            </div>
          </div>
        `;
      },
      /*    <div class="card-link">
                  <a href="${data.url}" target="_blank">
                    <i class="el-icon-link"></i>
                    <span>查看详情</span>
                  </a>
                </div> */
      /**
       * 查看已确认人员
       * @param {Object} cardData - 卡片数据
       */
      async handleViewConfirmedUsers(cardData) {
        this.currentCardData = cardData;
        // return console.log(cardData);
        try {
          // 调用接口获取已确认人员列表
          const response = await getCardUserList({
            // 根据实际接口参数进行调整
            messageId: cardData.messageId,
            pageNum: 1,
            pageSize: 100
          });
          this.confirmedUsersList = response.data || [];
          this.showConfirmedUsers = true;
        } catch (error) {
          console.error('获取已确认人员失败:', error);
          this.$message.error('获取已确认人员失败');
        }
      },

      /**
       * 初始化全局事件处理
       */
      initGlobalHandlers() {
        // 添加全局方法以处理卡片中的按钮点击
        window.viewConfirmedUsers = (dataStr) => {
          const cardData = JSON.parse(decodeURIComponent(dataStr));
          this.handleViewConfirmedUsers(cardData);
        };
      },

      /**
       * 关闭转发详情弹窗
       */
      handleRelayDetailClose() {
        this.showRelayDetail = false;
        this.currentRelayMessage = null;
      },

      /**
       * 关闭视频预览弹窗
       */
      handleVideoClose() {
        this.showVideoPreview = false;
        this.currentVideo = null;
      },
      /**
       * 格式化视频信息
       * @param {string|Object} content - 视频内容
       * @param {string} key - 需要获取的属性
       * @returns {string} 格式化后的视频信息
       */
      formatVideo(content, key) {
        try {
          const data = typeof content === 'string' ? JSON.parse(content) : content;
          return data[key] || '';
        } catch (e) {
          return '';
        }
      },
      isMessageCollapsed(message) {
        return this.collapsedMessages.has(message.id);
      },
      /**
       * 滚动到高亮消息
       */
      scrollToHighlighted(index) {
        const chatList = this.$refs.chatList;
        if (!chatList) return;

        const messageElements = chatList.getElementsByClassName('message-item');
        if (messageElements && messageElements[index]) {
          messageElements[index].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      },

      /**
       * 判断消息是否需要高亮
       */
      shouldHighlight(index) {
        return this.highlightedMessages.has(index);
      },

      /**
       * 重置筛选和高亮状态
       */
      resetFilters() {
        this.searchKey = '';
        this.filterSender = '';
        this.filterDate = null;
        this.filterType = '';
        this.highlightedMessages.clear();
        this.searchResults = { total: 0, current: -1 };
        this.emitFilterChange();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .chat-history-container {
    display: flex;
    flex-direction: column;
    // height: 100%;
    height: calc(100vh - 200px);
    background: #fff;
    position: relative; // 添加相对定位
  }

  .search-filter-section {
    padding: 8px 24px;
    background: #fff;
    display: flex;
    gap: 16px;
    align-items: center;
    position: sticky; // 使用sticky定位
    top: 0; // 固定在顶部
    z-index: 10; // 确保在其他内容之上
    border-bottom: 1px solid #ebeef5; // 添加底部边框

    .search-box {
      flex: 1;

      :deep(.el-input__inner) {
        border-radius: 4px;

        &:focus {
          border-color: #409eff;
        }
      }

      :deep(.el-input__prefix) {
        left: 12px;
        color: #909399;
      }

      :deep(.el-input__inner) {
        padding-left: 36px;
      }
    }

    .filter-box {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  .filter-box {
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 8px 24px;
    background: #fff;
    .sender-select {
      width: 90px;
      border: none !important;
      :deep(.el-input--small .el-input__inner) {
        border: none !important;
      }
      :deep(.el-input__inner) {
        border-radius: 4px;
        border: none !important;
      }
    }

    .date-picker {
      width: 150px;

      :deep(.el-range-editor) {
        border-radius: 4px;
      }

      :deep(.el-range-input) {
        font-size: 13px;
      }

      :deep(.el-range-separator) {
        color: #909399;
      }
    }
  }
  .message-type-tabs {
    padding: 6px 24px;
    background: #fff;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: center;

    .el-radio-group {
      width: 240px;

      :deep(.el-radio-button__inner) {
        padding: 8px 0;
        font-size: 13px;
        border-color: #dcdfe6;

        &:hover {
          color: #409eff;
        }
      }

      :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
        background-color: #409eff;
        border-color: #409eff;
        box-shadow: -1px 0 0 0 #409eff;
      }
    }
  }

  .chat-list {
    // flex: 1;
    // height: 100%;
    height: calc(100vh - 280px);
    overflow-y: auto;
    padding: 16px 24px;
    margin-top: 8px; // 添加一些顶部间距
    background: #f5f7fa;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .message-group {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .date-divider {
    text-align: center;
    color: #909399;
    font-size: 12px;
    margin: 24px 0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1px;
      background: #dcdfe6;
      margin: 0 16px;
    }
  }

  .message-item {
    margin-bottom: 16px;
    max-width: 70%;
    display: flex;
    flex-direction: column;
    &.self {
      margin-left: auto;
      align-items: flex-end;

      .message-content {
        background: #e1f3ff;
        border: 1px solid rgba(64, 158, 255, 0.1);
      }

      .message-header {
        text-align: right;
      }
    }

    &.other {
      margin-right: auto;
      align-items: flex-start;

      .message-content {
        background: #fff;
        border: 1px solid #ebeef5;
      }
    }
  }

  .message-header {
    margin-bottom: 6px;
    font-size: 12px;
    padding: 0 4px;
    display: flex;
    align-items: center;

    .sender-name {
      color: #606266;
      margin-right: 8px;
      font-weight: 500;
      &.other {
        color: #409eff;
      }
    }

    .message-time {
      color: #909399;
    }

    .message-tag {
      margin-left: 8px;
      padding: 2px 10px;
      background: #a4adb3;
      border-radius: 5px;
      color: #fff;
      font-size: 11px;
      margin-bottom: 2px;
    }

    .collapse-btn {
      margin-left: auto;
      padding: 0;
      color: #909399;

      &:hover {
        color: #409eff;
      }
    }
  }

  .message-content {
    padding: 12px 16px;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    position: relative;
    word-break: break-all;
    font-size: 14px;
    line-height: 1.6;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    &.file {
      padding: 0;

      .file-message {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 12px 16px;
        transition: background-color 0.3s;

        &:hover {
          background-color: rgba(64, 158, 255, 0.04);
        }

        .file-icon {
          font-size: 28px;
          margin-right: 12px;
          color: #409eff;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            transition: transform 0.3s;
          }

          &:hover i {
            transform: scale(1.1);
          }
        }

        .file-info {
          flex: 1;
          min-width: 0;

          .file-name {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .file-size {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    &.image {
      padding: 4px;
      overflow: hidden;
      background: #fff;

      img {
        display: block;
        max-width: 100%;
        cursor: zoom-in;
        border-radius: 2px;
        transition: transform 0.3s;

        &:hover {
          transform: scale(1.02);
        }
      }
    }
  }

  .image-preview-dialog {
    :deep(.el-dialog) {
      margin: 0 !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-width: 90%;
      max-height: 90vh;

      .el-dialog__body {
        padding: 0;

        img {
          display: block;
          max-width: 100%;
          max-height: 80vh;
          object-fit: contain;
        }
      }
    }
  }

  :deep(.el-select-dropdown__item) {
    padding: 0 16px;
    height: 32px;
    line-height: 32px;
    font-size: 13px;
  }

  :deep(.el-picker-panel) {
    border-radius: 4px;

    .el-picker-panel__sidebar {
      background: #f5f7fa;
    }

    .el-picker-panel__shortcut {
      font-size: 13px;
      padding: 8px 16px;

      &:hover {
        color: #409eff;
      }
    }
  }
  /* 视频消息样式 */
  .video-wrapper {
    max-width: 350px;
    border-radius: 4px;
    overflow: hidden;
    background: #000;
  }

  .video-wrapper video {
    width: 100%;
    vertical-align: middle;
  }
  /* 语音消息样式 */
  .audio-wrapper {
    min-width: 80px;
    max-width: 200px;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    background: #fff;
    border: 1px solid #ebeef5;
  }

  .audio-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzQwOWVmZiIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0xIDE1bC0uMDEtMTBoMnYxMGgtMnptNCAwaC0ydi0xMGgydjEweiIvPjwvc3ZnPg==')
      no-repeat center;
    background-size: contain;
    margin-right: 8px;
    animation: audioWave 1.5s infinite;
  }

  @keyframes audioWave {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }

  .audio-duration {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
  }

  /* 自己发送的语音消息样式 */
  .message-item.self .audio-wrapper {
    background: #e1f3ff;
    border: 1px solid rgba(64, 158, 255, 0.1);
    flex-direction: row-reverse;
  }

  .message-item.self .audio-icon {
    margin-right: 0;
    margin-left: 8px;
    transform: rotate(180deg);
  }

  .message-item.self .audio-duration {
    color: #409eff;
  }

  /* 语音消息悬停效果 */
  .audio-wrapper:hover {
    background: #f5f7fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .message-item.self .audio-wrapper:hover {
    background: #d0e8ff;
  }

  /* 表情消息样式 */
  .face-message {
    padding: 8px;

    .face-image {
      width: 24px;
      height: 24px;
      vertical-align: middle;
    }
  }

  /* 自定义消息样式 */
  .custom-message {
    padding: 12px 16px;
    background: #fff;
    border-radius: 4px;

    .custom-content {
      font-size: 14px;
      color: #303133;
      line-height: 1.6;
    }
  }

  /* 合并转发消息样式优化 */
  .relay-message {
    padding: 12px 16px;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    max-width: 400px;

    &:hover {
      background-color: rgba(64, 158, 255, 0.04);
    }

    .relay-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .relay-title {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
        margin-right: 8px;
      }

      .relay-count {
        font-size: 12px;
        color: #909399;
      }
    }

    .relay-preview {
      .relay-item {
        font-size: 13px;
        color: #606266;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .relay-content {
          color: #409eff;
          margin-right: 4px;
        }
      }

      .relay-more {
        font-size: 12px;
        color: #909399;
        text-align: center;
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px dashed #ebeef5;
      }
    }
  }

  /* 转发详情弹窗样式优化 */
  .relay-detail-dialog {
    :deep(.el-dialog__body) {
      padding: 0;
    }
  }

  .relay-detail-container {
    height: 60vh;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;

    .relay-detail-header {
      padding: 0 16px;
      background: #fff;
      border-bottom: 1px solid #ebeef5;

      .relay-detail-title {
        font-size: 16px;
        color: #303133;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .relay-detail-count {
        font-size: 12px;
        color: #909399;
      }
    }

    .relay-detail-list {
      flex: 1;
      overflow-y: auto;
      padding: 16px;

      .relay-detail-message {
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
        max-width: 80%;

        &.self {
          margin-left: auto;
          align-items: flex-end;

          .relay-detail-content {
            .text-message {
              background: #e1f3ff;
              border: 1px solid rgba(64, 158, 255, 0.1);
            }

            .file-message {
              background: #e1f3ff;
              border: 1px solid rgba(64, 158, 255, 0.1);
            }
          }
        }

        &.other {
          margin-right: auto;
          align-items: flex-start;

          .relay-detail-content {
            .text-message {
              background: #fff;
              border: 1px solid #ebeef5;
            }

            .file-message {
              background: #fff;
              border: 1px solid #ebeef5;
            }
          }
        }

        .relay-detail-sender {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 8px;
          }

          .sender-name {
            font-size: 13px;
            color: #606266;
            margin-right: 8px;
            &.other {
              color: #409eff;
            }
          }

          .role-tag {
            // height: 20px;
            // line-height: 18px;
            // padding: 0 6px;
            // font-size: 12px;
            margin-left: 5px;
            padding: 2px 10px;
            background: #a4adb3;
            border-radius: 5px;
            color: #fff;
            font-size: 11px;
            margin-bottom: 2px;
          }
        }

        .relay-detail-content {
          .text-message {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.6;
            word-break: break-all;
          }

          .image-message {
            img {
              max-width: 200px;
              max-height: 200px;
              cursor: pointer;
              border-radius: 4px;
              border: 1px solid #ebeef5;
            }
          }

          .file-message {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }

            .file-icon {
              font-size: 24px;
              color: #409eff;
              margin-right: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              background: rgba(64, 158, 255, 0.1);
              border-radius: 4px;

              i {
                transition: transform 0.3s;
              }

              &:hover i {
                transform: scale(1.1);
              }
            }

            .file-info {
              flex: 1;
              min-width: 0;

              .file-name {
                font-size: 14px;
                color: #303133;
                margin-bottom: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .file-size {
                font-size: 12px;
                color: #909399;
              }
            }
          }
        }
      }
    }
  }

  /* 高亮搜索结果的样式 */
  .message-item.highlight {
    .message-content {
      background-color: rgba(255, 230, 0, 0.2);
      border-color: rgba(255, 193, 7, 0.5);
    }
  }

  /* 搜索框样式优化 */
  .search-box {
    position: relative;

    .search-count {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;
      color: #909399;
      background: #f5f7fa;
      padding: 2px 6px;
      border-radius: 10px;
    }
  }

  /* 日期选择器样式优化 */
  .date-picker {
    :deep(.el-input__inner) {
      padding-right: 30px;
    }

    :deep(.el-input__suffix) {
      right: 0;
    }
  }

  /* 发送人选择器样式优化 */
  .sender-select {
    :deep(.el-input__inner) {
      padding-right: 30px;
    }
  }

  /* 文件消息样式优化 */
  .file-message {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    cursor: pointer;
    transition: all 0.3s;
    max-width: 300px;

    &:hover {
      background-color: #f5f7fa;
      transform: translateY(-1px);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .file-icon {
      font-size: 32px;
      color: #409eff;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: rgba(64, 158, 255, 0.1);
      border-radius: 4px;

      i {
        transition: transform 0.3s;
      }

      &:hover i {
        transform: scale(1.1);
      }
    }

    .file-info {
      flex: 1;
      min-width: 0;

      .file-name {
        font-size: 14px;
        color: #303133;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .file-size {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  /* 视频预览弹窗样式 */
  .video-preview-dialog {
    :deep(.el-dialog__body) {
      padding: 0;
    }
  }

  .video-preview-container {
    background: #000;
    border-radius: 4px;
    overflow: hidden;

    .video-player {
      width: 100%;
      max-height: 70vh;
      display: block;
    }

    .video-info {
      padding: 12px;
      background: #fff;
      border-top: 1px solid #ebeef5;

      .video-name {
        font-size: 14px;
        color: #303133;
        margin-bottom: 4px;
      }

      .video-size {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  /* 已确认人员弹窗样式 */
  .confirmed-users-dialog {
    .confirmed-users-list {
      max-height: 400px;
      overflow-y: auto;

      .confirmed-user-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 12px;
        }

        .user-info {
          flex: 1;

          .user-name {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
          }

          .confirm-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .no-data {
      text-align: center;
      color: #909399;
      padding: 24px;
      font-size: 14px;
    }
  }
</style>
<style>
  .el-input__prefix,
  .el-input__suffix {
    height: 32px !important;
    display: flex;
    align-items: center;
  }

  /* 自定义卡片消息样式 - 非scoped样式，用于v-html动态插入的内容 */
  .custom-card-message {
    background: #fff;
    border-radius: 12px;
    border: 1px solid #ebeef5;
    overflow: hidden;
    width: 350px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .custom-card-message:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  }

  .custom-card-message .card-header {
    padding: 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f1fc 100%);
    border-bottom: 1px solid #e6f1fc;
  }

  .custom-card-message .card-header .card-title {
    font-size: 16px;
    font-weight: 700;
    color: #303133;
    margin-bottom: 4px;
  }

  .custom-card-message .card-header .card-subtitle {
    font-size: 12px;
    color: #909399;
  }

  .custom-card-message .card-content {
    padding: 16px;
  }

  .custom-card-message .card-content .card-time {
    display: flex;
    align-items: center;
    color: #606266;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .custom-card-message .card-content .card-time i {
    margin-right: 8px;
    color: #409eff;
  }

  .custom-card-message .card-content .card-teacher {
    display: flex;
    align-items: center;
    margin: 12px 0;
    font-size: 14px;
  }

  .custom-card-message .card-content .card-teacher i {
    margin-right: 8px;
    color: #409eff;
  }

  .custom-card-message .card-content .card-teacher .label {
    color: #606266;
  }

  .custom-card-message .card-content .card-confirm {
    margin-top: 16px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 8px;
  }

  .custom-card-message .card-content .card-confirm .confirm-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
    margin-bottom: 12px;
  }

  .custom-card-message .card-content .card-confirm .confirm-text i {
    margin-right: 8px;
    color: #409eff;
  }

  .custom-card-message .card-content .card-confirm .confirm-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  .custom-card-message .card-content .card-confirm .confirm-buttons button {
    display: flex;
    align-items: center;
    padding: 6px 16px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
  }

  .custom-card-message .card-content .card-confirm .confirm-buttons button i {
    margin-right: 4px;
  }

  .custom-card-message .card-content .card-confirm .confirm-buttons button.view-btn {
    background: #f5f7fa;
    color: #606266;
    border: 1px solid #dcdfe6;
  }

  .custom-card-message .card-content .card-confirm .confirm-buttons button.view-btn:hover {
    background: #e6e8eb;
  }

  .custom-card-message .card-content .card-confirm .confirm-buttons button.confirm-btn {
    background: #409eff;
    color: #fff;
  }

  .custom-card-message .card-content .card-confirm .confirm-buttons button.confirm-btn:hover {
    background: #66b1ff;
  }

  .custom-card-message .card-content .card-image {
    margin: 12px 0;
    border-radius: 8px;
    overflow: hidden;
  }

  .custom-card-message .card-content .card-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s;
  }

  .custom-card-message .card-content .card-image img:hover {
    transform: scale(1.02);
  }

  .custom-card-message .card-content .card-link {
    margin-top: 12px;
  }

  .custom-card-message .card-content .card-link a {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f5f7fa;
    border-radius: 6px;
    color: #606266;
    text-decoration: none;
    transition: all 0.3s;
  }

  .custom-card-message .card-content .card-link a i {
    margin-right: 8px;
    color: #409eff;
  }

  .custom-card-message .card-content .card-link a span {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .custom-card-message .card-content .card-link a:hover {
    background: #e6e8eb;
    color: #409eff;
  }

  /* 不同类型卡片的特殊样式 */
  .custom-card-message.confirm-card .card-header {
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f1fc 100%);
  }

  .custom-card-message.link-card .card-header {
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f1fc 100%);
  }

  .custom-card-message.default-card .card-header {
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f1fc 100%);
  }

  .custom-card-message.dispatch-card .card-header {
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f1fc 100%);
  }

  /* 纯文本消息卡片样式 */
  .custom-card-message.text-message-card {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e8f4fd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    max-width: 400px;
    margin: 4px 0;
  }

  .custom-card-message.text-message-card .card-content {
    padding: 16px 20px;
  }

  .custom-card-message.text-message-card .text-content {
    font-size: 15px;
    line-height: 1.6;
    color: #2c3e50;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  .custom-card-message.text-message-card .emoji {
    display: inline-block;
    background: #ffeaa7;
    color: #d63031;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 12px;
    margin: 0 2px;
    font-weight: 500;
  }

  /* 报告消息卡片样式 */
  .custom-card-message.report-card {
    background: linear-gradient(135deg, #fff 0%, #f8fcff 100%);
    border: 1px solid #e1f0fe;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  }

  .custom-card-message.report-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    display: flex;
    align-items: center;
    padding: 16px 20px;
  }

  .custom-card-message.report-card .card-icon {
    margin-right: 12px;
    font-size: 20px;
    opacity: 0.9;
  }

  .custom-card-message.report-card .card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: #fff;
  }

  .custom-card-message.report-card .card-content {
    padding: 20px;
  }

  .custom-card-message.report-card .card-description {
    margin-bottom: 16px;
  }

  .custom-card-message.report-card .card-description p {
    margin: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.5;
  }

  .custom-card-message.report-card .report-btn {
    display: inline-flex;
    align-items: center;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }

  .custom-card-message.report-card .report-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
    color: #fff;
  }

  .custom-card-message.report-card .report-btn i {
    margin-right: 8px;
    font-size: 16px;
  }

  /* 课程确认卡片样式 */
  .custom-card-message.class-confirm-card {
    background: linear-gradient(135deg, #fff 0%, #f9fcff 100%);
    border: 1px solid #e1f0fe;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.12);
    max-width: 420px;
  }

  .custom-card-message.class-confirm-card .card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: #fff;
    display: flex;
    align-items: center;
    padding: 16px 20px;
  }

  .custom-card-message.class-confirm-card .card-icon {
    margin-right: 12px;
    font-size: 22px;
    opacity: 0.9;
  }

  .custom-card-message.class-confirm-card .card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: #fff;
  }

  .custom-card-message.class-confirm-card .card-content {
    padding: 20px;
  }

  .custom-card-message.class-confirm-card .course-info {
    margin-bottom: 20px;
  }

  .custom-card-message.class-confirm-card .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: #f8fbff;
    border-radius: 8px;
    border-left: 3px solid #4facfe;
    transition: all 0.3s ease;
  }

  .custom-card-message.class-confirm-card .info-item:hover {
    background: #f0f8ff;
    transform: translateX(2px);
  }

  .custom-card-message.class-confirm-card .info-item:last-child {
    margin-bottom: 0;
  }

  .custom-card-message.class-confirm-card .info-label {
    display: flex;
    align-items: center;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
  }

  .custom-card-message.class-confirm-card .info-label i {
    margin-right: 8px;
    color: #4facfe;
    font-size: 16px;
  }

  .custom-card-message.class-confirm-card .info-value {
    color: #2d3748;
    font-size: 14px;
    font-weight: 600;
    max-width: 200px;
    text-align: right;
    word-break: break-all;
  }

  .custom-card-message.class-confirm-card .confirm-actions {
    border-top: 1px solid #e2e8f0;
    padding-top: 16px;
  }

  .custom-card-message.class-confirm-card .confirm-note {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #fff7ed;
    border-radius: 6px;
    border-left: 3px solid #f59e0b;
  }

  .custom-card-message.class-confirm-card .confirm-note i {
    margin-right: 8px;
    color: #f59e0b;
    font-size: 16px;
  }

  .custom-card-message.class-confirm-card .confirm-note span {
    color: #92400e;
    font-size: 13px;
    font-weight: 500;
  }

  .custom-card-message.class-confirm-card .action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
  }

  .custom-card-message.class-confirm-card .action-buttons button {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    justify-content: center;
  }

  .custom-card-message.class-confirm-card .confirm-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  }

  .custom-card-message.class-confirm-card .confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
  }

  .custom-card-message.class-confirm-card .modify-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  }

  .custom-card-message.class-confirm-card .modify-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
  }

  .custom-card-message.class-confirm-card .action-buttons button i {
    margin-right: 6px;
    font-size: 14px;
  }

  /* 派单卡片样式 */
  .custom-card-message.dispatch-order-card {
    background: linear-gradient(135deg, #fff 0%, #fefbf3 100%);
    border: 1px solid #f0e68c;
    box-shadow: 0 4px 16px rgba(255, 193, 7, 0.15);
    max-width: 480px;
  }

  .custom-card-message.dispatch-order-card .card-header {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
    color: #fff;
    display: flex;
    align-items: center;
    padding: 16px 20px;
  }

  .custom-card-message.dispatch-order-card .card-icon {
    margin-right: 12px;
    font-size: 22px;
    opacity: 0.9;
  }

  .custom-card-message.dispatch-order-card .card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: #fff;
  }

  .custom-card-message.dispatch-order-card .card-content {
    padding: 20px;
  }

  .custom-card-message.dispatch-order-card .info-section {
    margin-bottom: 20px;
  }

  .custom-card-message.dispatch-order-card .info-section:last-child {
    margin-bottom: 0;
  }

  .custom-card-message.dispatch-order-card .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 2px solid #fff3cd;
    color: #856404;
    font-weight: 600;
    font-size: 14px;
  }

  .custom-card-message.dispatch-order-card .section-title i {
    margin-right: 8px;
    color: #ff9a56;
    font-size: 16px;
  }

  .custom-card-message.dispatch-order-card .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .custom-card-message.dispatch-order-card .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 14px;
    background: #fffbf0;
    border-radius: 6px;
    border-left: 3px solid #ff9a56;
    transition: all 0.3s ease;
  }

  .custom-card-message.dispatch-order-card .info-item:hover {
    background: #fff8e1;
    transform: translateX(2px);
  }

  .custom-card-message.dispatch-order-card .info-label {
    color: #8b5a00;
    font-size: 13px;
    font-weight: 500;
    min-width: 80px;
  }

  .custom-card-message.dispatch-order-card .info-value {
    color: #2d3748;
    font-size: 13px;
    font-weight: 600;
    text-align: right;
    flex: 1;
    margin-left: 12px;
    word-break: break-all;
  }

  .custom-card-message.dispatch-order-card .order-id {
    font-family: 'Courier New', monospace;
    background: #e3f2fd;
    color: #1565c0;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
  }

  .custom-card-message.dispatch-order-card .merchant-id {
    font-family: 'Courier New', monospace;
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
  }

  .custom-card-message.dispatch-order-card .remark-content {
    background: #fff8e1;
    border: 1px solid #ffcc02;
    border-radius: 6px;
    padding: 12px;
    color: #8b5a00;
    font-size: 13px;
    line-height: 1.5;
    margin-top: 8px;
  }
</style>
