/**
 * 投诉记录相关接口
 */
import request from '@/utils/request';
const prefix = 'im';

export default {
  /**
   * 投诉列表
   * @param data
   * @returns {*}
   */
  informationList(data) {
    return request({
      url: prefix + '/complaint/information',
      method: 'get',
      params: data
    });
  },

  /**
   * 查看详情
   */
  getRecordDetail(id) {
    return request({
      url: prefix + '/risk/record/details?id=' + id,
      method: 'get'
    });
  },

  /**
   * 获取聊天记录
   * @param data
   * @returns {*}
   */
  getMessageList(data) {
    return request({
      url: prefix + '/complaint/information/chat',
      method: 'get',
      params: data
    });
  },

  /**
   * 回复投诉
   * @param data
   * @returns {*}
   */
  replyInformation(data) {
    return request({
      url: prefix + '/complaint/information',
      method: 'put',
      data
    });
  }
};
