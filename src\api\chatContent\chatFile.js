/**
 * 会话存档接口
 */
import request from '@/utils/request';
/**
 * 获取身份列表
 * @param data
 * @returns {*}
 */
export const getRoleList = () => {
  return request({
    url: '/im/api/user/role',
    method: 'GET'
  });
};
/**
 * 身份选择后获取用户列表
 * @param data
 * @returns {*}
 */
export const getUserList = (data) => {
  return request({
    url: '/im/api/user/list',
    method: 'POST',
    data
  });
};
/**
 * 获取单聊用户列表
 * @param data
 * @returns {*}
 */
export const getChatUserListSingle = (data) => {
  return request({
    url: '/im/api/chat/interlocutor',
    method: 'GET',
    params: data
  });
};
/**
 * 获取群聊列表
 * @param data
 * @returns {*}
 */
export const getChatUserListGroup = (data) => {
  return request({
    url: '/im/api/chat/group-interlocutor',
    method: 'GET',
    params: data
  });
};
/**
 * 获取单聊聊天记录
 * @param data
 * @returns {*}
 */
export const getChatListSingle = (data) => {
  return request({
    url: '/im/api/chat/messages',
    method: 'GET',
    params: data
  });
};
/**
 * 获取群聊聊天记录
 * @param data
 * @returns {*}
 */
export const getChatListGroup = (data) => {
  return request({
    url: '/im/api/chat/group-messages',
    method: 'GET',
    params: data
  });
};

/**
 * 获取群聊信息
 * @param data
 * @returns {*}
 */
export const getGroupInfoApi = (data) => {
  return request({
    url: '/im/api/group/get-data',
    method: 'GET',
    params: data
  });
};
/**
 * 获取文件记录
 * @param data
 * @returns {*}
 */
export const getFileList = (data) => {
  return request({
    url: '/im/api/chat/file-record',
    method: 'GET',
    params: data
  });
};
/**
 * 获取群聊成员
 * @param data
 * @returns {*}
 */
export const getGroupUserList = (data) => {
  return request({
    url: '/im/api/chat/group-members',
    method: 'GET',
    params: data
  });
};
/**
 * 获取已确认成员
 * @param data
 * @returns {*}
 */
export const getCardUserList = (data) => {
  return request({
    url: '/im/api/chat/query-card-user',
    method: 'GET',
    params: data
  });
};

/**
 * 获取群聊列表
 */
export const getGroupListApi = (data) => {
  return request({
    url: 'im/api/group/get-groups-data',
    method: 'GET',
    params: data
  });
};
