/**
 * 群继承接口
 */
import request from "@/utils/request";
/**
  * 获取应用列表
  * @param data
  * @returns {*}
  */
export const getAppList = (data) => {
  return request({
    url: "/im/api/app/get-app-list",
    method: "GET",
    params: data
  });
}
/**
  * 获取群聊列表
  * @param data
  * @returns {*}
  */
export const getGroupList = (data) => {
  return request({
    url: "/im/api/group/get-group-list",
    method: "GET",
    params: data
  });
}
/**
  * 获取应用对应群主列表
  * @param data
  * @returns {*}
  */
export const getGroupOwnerList = (data) => {
  return request({
    url: "/im/api/group/get-group-owner",
    method: "GET",
    params: data
  });
}
/**
  * 获取应用对应群主列表
  * @param data
  * @returns {*}
  */
export const getGroupListByOwner = (data) => {
  return request({
    url: "/im/api/group/get-group-by-owner",
    method: "POST",
    data
  });
}

/**
  * 获取应用下用户列表
  * @param data
  * @returns {*}
  */
export const getUserListByApp = (data) => {
  return request({
    url: "/im/api/user/get-all-user",
    method: "GET",
    params: data
  });
}
/**
  * 获取应用下用户列表
  * @param data
  * @returns {*}
  */
export const inheritGroup = (data) => {
  return request({
    url: "/im/api/group/inherit-group",
    method: "POST",
    data
  });
}

