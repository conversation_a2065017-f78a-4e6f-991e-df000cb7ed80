const ls = window.sessionStorage;
export
default {
    // 获取
    getItem(key) {
        try {
            return JSON.parse(ls.getItem(key));
        } catch (err) {
            return null;
        }
    },
    // 设置
    setItem(key, val) {
        ls.setItem(key, JSON.stringify(val));
    },
    // 清除所有
    clear() {
        ls.clear();
    },
    // 获取指定下标对应数据的key名
    keys(index) {
        return ls.key(index);
    },
    // 移除某一个指定key的对应数据
    removeItem(key) {
        ls.removeItem(key);
    }
}
