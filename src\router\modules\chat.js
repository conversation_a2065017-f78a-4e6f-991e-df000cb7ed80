/** When your routing table is too long, you can split it into small modules**/

import Layout from '@/layout';
const chatRouter = {
  path: '/chatContent',
  component: Layout,
  name: 'chatContent',
  meta: {
    title: '会话内容',
    icon: 'chart',
    perm: 'm:chatContent'
  },
  alwaysShow: true,
  children: [
    {
      path: 'chatFile',
      component: () => import('@/views/chatContent/chatFile'),
      name: 'chatFile',
      meta: { title: '用户会话', noCache: true, affix: false, perm: 'm:chatContent:chatFile', icon: 'studentList' }
    },
    {
      path: 'groupFile',
      component: () => import('@/views/chatContent/groupFile'),
      name: 'groupFile',
      meta: { title: '群聊会话', noCache: true, affix: false, perm: 'm:chatContent:groupFile', icon: 'studentList' }
    }
  ]
};

export default chatRouter;
