<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="敏感词表" name="sensitive"></el-tab-pane>
      <el-tab-pane label="过滤词表" name="filter"></el-tab-pane>
    </el-tabs>

    <!-- 搜索表单区域 -->
    <div>
      <el-form ref="searchForm" :model="searchForm" inline label-width="90px">
        <el-form-item v-if="activeTab === 'sensitive'" :prop="currentWordField" label="词表名称：">
          <el-input v-model.trim="searchForm.sensitiveWordName" clearable placeholder="请输入词表名称" size="small" />
        </el-form-item>
        <el-form-item v-if="activeTab === 'filter'" :prop="currentWordField" label="词表名称：">
          <el-input v-model.trim="searchForm.filterWordName" clearable placeholder="请输入词表名称" size="small" />
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" size="small" type="primary" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh-left" size="small" @click="handleReset">重置</el-button>
          <el-button icon="el-icon-plus" size="small" type="primary" @click="handleAdd">新增</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div v-loading="loading" class="table-container">
      <div v-show="!allData.length" class="no-more">
        <el-image src="https://document.dxznjy.com/automation/1728442200000" style="width: 100px; height: 100px" />
        <div style="color: #999; margin-top: 20px">暂无数据</div>
      </div>

      <el-table v-show="allData.length" :data="allData" :header-cell-style="getRowClass" fit size="large" style="width: 100%">
        <el-table-column align="center" header-align="center" label="序号" type="index" width="100" />
        <el-table-column :prop="wordNameField" label="词表名称">
          <template v-slot="scope">
            <el-tooltip :content="scope.row[wordNameField]" placement="top">
              <span class="rule-name" @click="viewWordList(scope.row, wordNameField)">
                {{ scope.row[wordNameField] }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :prop="wordNumField" header-align="left" label="词数量" />
        <el-table-column header-align="left" label="最近修改人" prop="updateUser" />
        <el-table-column header-align="left" label="修改时间" prop="updateTime" />
        <el-table-column header-align="left" label="操作" width="200">
          <template v-slot="scope">
            <el-button v-if="canEdit(scope.row)" type="text" @click.stop="editWordList(scope.row, wordNumField)">编辑</el-button>
            <el-button v-if="canEdit(scope.row)" type="text" @click.stop="copyWordList(scope.row, wordNumField)">复制</el-button>
            <el-button v-if="canEdit(scope.row)" style="color: #ff4d4f" type="text" @click.stop="deleteWordList(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页器 -->
    <el-row align="middle" class="pagination-container" justify="left" type="flex">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>

    <!-- 查看/编辑词表内容 -->
    <DrawWordList
      ref="drawWordList"
      :current-word-list="currentWordList"
      :drawer-mode="drawerMode"
      :importType="importType"
      :tablePagination="tableViewPagination"
      :visible="drawerVisible"
      @close="handleDrawerClose"
      @save="handleSaveWordList"
      @current-change-view="currentViewChange"
      @size-change-view="sizeViewChange"
    />

    <!-- 新增/编辑词表 -->
    <AddWordList
      ref="addWordList"
      :current-word-list="currentWordList"
      :importType="importType"
      :is-editing="isEditing"
      :tablePagination="tablePagination"
      :visible="addDrawerVisible"
      @close="handleAddDrawerClose"
      @editSuccess="editSuccess"
      @save="handleSaveNewWordList"
      @current-change="currentChange"
      @size-change="sizeChange"
    />

    <!-- 批量导入 -->
    <batchAddManagement ref="batchAddManagementRef" :dialogOpen="batchDialogVisible" :importType="importType" @closeBatchDialog="closeBatchDialog" @updateTable="updateTable" />

    <!-- 新增敏感词弹窗 -->
    <el-dialog :before-close="handleDialogClose" :close-on-click-modal="false" :title="currentDialogText" :visible.sync="dialogVisible" width="30%">
      <el-form ref="dialogForm" :model="dialogForm" :rules="dialogRules">
        <el-form-item label="词表名称" prop="tableName">
          <el-input v-model="dialogForm.tableName" :placeholder="'请输入' + (importType === 'sensitive' ? '敏感词' : '过滤词') + '表名称'" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item :label="importType === 'sensitive' ? '敏感词' : '过滤词'" prop="sensitiveWords">
          <el-input
            v-if="importType === 'sensitive'"
            v-model="dialogForm.sensitiveWords"
            :rows="8"
            placeholder="请输入敏感词，用“、”隔开多个敏感词，单个敏感词不超过20个字"
            show-word-limit
            type="textarea"
          />
          <el-input v-else v-model="dialogForm.sensitiveWords" :rows="8" placeholder="请输入过滤词，用“、”隔开多个过滤词，单个过滤词不超过20个字" show-word-limit type="textarea" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button :disabled="disabledLoading" type="primary" @click="confirmAddWords">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import DrawWordList from '@/views/riskAudit/riskSetting/components/DrawWordList.vue';
  import AddWordList from '@/views/riskAudit/riskSetting/components/AddWordList.vue';
  import BatchAddManagement from '@/views/riskAudit/riskSetting/components/batchAddManagement.vue';
  import sensitiveWordsApi from '@/api/riskAudit/riskSetting';

  export default {
    name: 'SensitiveWordsList',
    components: { BatchAddManagement, DrawWordList, AddWordList },
    data() {
      return {
        activeTab: 'sensitive',
        searchForm: { sensitiveWordName: undefined, pageNumber: 1, pageSize: 10, filterWordName: undefined },
        allData: [],
        loading: false,
        pagination: { currentPage: 1, pageSize: 10, total: 0 },
        drawerVisible: false,
        drawerMode: 'view', // 'view' 或 'edit'
        addDrawerVisible: false,
        batchDialogVisible: false,
        isEditing: false,
        currentWordList: {},
        userRole: 'admin',
        currentUser: 'user1',
        importType: 'sensitive',
        textMap: {
          update: '编辑敏感词',
          create: '新增敏感词',
          updateFilters: '编辑过滤词',
          createFilters: '新增过滤词'
        },
        dialogForm: {
          sensitiveWords: '',
          tableName: ''
        },
        dialogRules: {
          sensitiveWords: [
            { required: true, message: '请输入敏感词或过滤词', trigger: 'blur' },
            {
              validator: (_rule, value, callback) => {
                const words = value
                  .split('、')
                  .map((word) => word.trim())
                  .filter(Boolean);
                if (words.length === 0) {
                  callback(new Error('请输入有效的敏感词或过滤词'));
                  return;
                }
                const invalidWords = words.filter((word) => word.length > 20);
                if (invalidWords.length > 0) {
                  callback(new Error(`敏感词"${invalidWords[0]}"超过20个字符限制`));
                  return;
                }
                callback();
              },
              trigger: ['blur', 'change']
            }
          ],
          tableName: [
            { required: true, message: '请输入词表名称', trigger: ['blur', 'change'] },
            { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
          ]
        },
        dialogStatus: '',
        currentDialogText: '',
        dialogVisible: false,
        querySearch: {
          pageNum: 1,
          pageSize: 10,
          sensitiveWordCode: ''
        },
        disabledLoading: false,
        tablePagination: { currentPage: 1, pageSize: 10, total: 0 },
        tableViewPagination: { currentPage: 1, pageSize: 10, total: 0 },
        choooseRowWords: {},
        choooseRowViewWords: {},
        FilterWordCode: ''
      };
    },

    computed: {
      currentWordField() {
        return this.activeTab === 'sensitive' ? 'sensitiveWordName' : 'filterWordName';
      },
      currentWordValue: {
        get() {
          return this.searchForm[this.currentWordField];
        },
        set(value) {
          this.$set(this.searchForm, this.currentWordField, value);
        }
      },
      wordNameField() {
        return this.activeTab === 'sensitive' ? 'sensitiveWordName' : 'filterWordName';
      },
      wordNumField() {
        return this.activeTab === 'sensitive' ? 'sensitiveWordNum' : 'filterWordNum';
      }
    },

    created() {
      this.loadData();
      this.$refs.batchAddManagementRef?.setDownLoadUrl('https://document.dxznjy.com/scrmRes/敏感词导入模板.xlsx');
    },

    methods: {
      currentChange(val) {
        console.log('分页值提示🚀🥶💩~ val', val);
        this.querySearch.pageNum = val;
        this.querySearch.pageSize = 10;
        this.editWordList(this.choooseRowWords, this.wordNumField);
      },
      sizeChange(val) {
        this.querySearch.pageNum = 1;
        this.querySearch.pageSize = val;
        this.editWordList(this.choooseRowWords, this.wordNumField);
      },

      //查看的修改当前页数
      currentViewChange(val) {
        this.querySearch.pageNum = val;
        this.querySearch.pageSize = 10;
        this.viewWordList(this.choooseRowViewWords, this.wordNameField);
      },
      //查看的修改当前分页值大小
      sizeViewChange(val) {
        this.querySearch.pageNum = 1;
        this.querySearch.pageSize = val;
        console.log('查看的修改当前分页值大小🚀🥶💩~ val', val);
        console.log('查看的修改当前分页值大小🚀🥶💩~ this.querySearch', this.querySearch);
        this.viewWordList(this.choooseRowViewWords, this.wordNameField);
      },

      handleDialogClose() {
        if (this.$refs.dialogForm) {
          this.$refs.dialogForm.resetFields();
        }
        this.dialogVisible = false;
        this.disabledLoading = false;
      },

      /**
       * @description 新增敏感词
       * @param {Object} data - sensitiveWordsApi.addSensitiveWords 的请求参数
       * @property {String} tableName - 表名
       * @property {String} sensitiveWords - 敏感词，多个词用“、”分隔
       * @return {Promise<void>} - 无返回值
       */
      async addWordItem() {
        try {
          const { code, data } = await sensitiveWordsApi.addSensitiveWords({
            tableName: this.dialogForm.tableName,
            sensitiveWords: this.dialogForm.sensitiveWords
          });
          if (code !== 20000) {
            this.$message.warning(data?.message || '新增失败');
            return;
          }
          this.$message.success('新增成功');
          this.dialogForm.tableName = '';
          this.dialogForm.sensitiveWords = '';
          if (this.$refs.dialogForm) {
            this.$refs.dialogForm.resetFields();
          }
          this.disabledLoading = false;
          this.dialogVisible = false;
          await this.loadData();
        } catch (error) {
          this.disabledLoading = false;

          // this.$message.error('新增失败，请重试');
          console.error('新增敏感词失败:', error);
        } finally {
        }
      },

      /**
       * @description 新增过滤词
       * @param {Object} data - sensitiveWordsApi.addFilterWords 的请求参数
       * @property {String} tableName - 表名
       * @property {String} filterWords - 过滤词，多个词用“、”分隔
       * @return {Promise<void>} - 无返回值
       */
      async addWordFilterItem() {
        try {
          const { code, data } = await sensitiveWordsApi.addFilterWords({
            tableName: this.dialogForm.tableName,
            filterWords: this.dialogForm.sensitiveWords
          });
          if (code !== 20000) {
            this.$message.warning(data?.message || '新增失败');
            return;
          }
          this.$message.success('新增成功');
          this.dialogForm.tableName = '';
          this.dialogForm.sensitiveWords = '';
          if (this.$refs.dialogForm) {
            this.$refs.dialogForm.resetFields();
          }
          await this.loadData();
          this.dialogVisible = false;
        } catch (error) {
          this.disabledLoading = false;

          // this.$message.error('新增失败，请重试');
          console.error('新增过滤词失败:', error);
        } finally {
        }
      },

      confirmAddWords() {
        this.$refs.dialogForm.validate((valid) => {
          if (!valid) return;
          this.disabledLoading = true;
          if (this.dialogForm.sensitiveWords.length > 5000) {
            const name = this.activeTab === 'sensitive' ? '敏感词' : '过滤词';
            this.$message.error(name + '数量不能大于5000');
            this.disabledLoading = false;
            return;
          }
          if (this.activeTab === 'sensitive') {
            this.addWordItem();
          } else {
            this.addWordFilterItem();
          }
        });
      },

      closeBatchDialog() {
        this.batchDialogVisible = false;
      },

      updateTable() {
        this.batchDialogVisible = false;
        this.loadData();
      },

      getRowClass({ rowIndex }) {
        return rowIndex === 0 ? 'background:#f5f7fa' : '';
      },

      /**
       * @description 加载敏感词或过滤词数据
       * @returns {Promise<void>} - 无返回值
       */
      async loadData() {
        this.loading = true;
        try {
          if (this.activeTab === 'sensitive') {
            const res = await sensitiveWordsApi.sensitiveWordsList(this.searchForm);
            this.allData = Array.isArray(res.data?.data) ? res.data.data : [];
            this.pagination.total = res.data?.total || 0;
          } else {
            const res = await sensitiveWordsApi.getFilterWords(this.searchForm);
            this.allData = Array.isArray(res.data?.data) ? res.data.data : [];
            this.pagination.total = res.data?.total || 0;
          }
        } catch (error) {
          console.error('数据加载失败:', error);
          this.$message.error('数据加载失败');
          this.allData = [];
          this.pagination.total = 0;
        } finally {
          this.loading = false;
        }
      },

      /**
       * @description 点击 tabs 事件处理函数
       * @param {*} tab 事件对象
       * @param {*} event 事件对象
       */
      handleTabClick() {
        this.importType = this.activeTab;
        this.pagination.currentPage = 1;
        this.querySearch.pageNum = 1;
        this.tablePagination.currentPage = 1;
        const otherField = this.activeTab === 'sensitive' ? 'filterWordName' : 'sensitiveWordName';
        this.$set(this.searchForm, otherField, '');
        const isSensitive = this.importType === 'sensitive';
        this.searchForm[isSensitive ? 'sensitiveWordName' : 'filterWordName'] = undefined;
        delete this.searchForm[isSensitive ? 'filterWordName' : 'sensitiveWordName'];
        this.$nextTick(() => {
          this.$refs.addWordList?.setDialogRules(this.importType);
        });
        this.downloadUrl = `https://document.dxznjy.com/scrmRes/${this.importType === 'sensitive' ? '敏感词导入模板' : '过滤词导入模板'}.xlsx`;
        this.$refs.batchAddManagementRef?.setDownLoadUrl(this.downloadUrl);
        this.loadData();
      },

      /**
       * @description 点击搜索按钮事件处理函数
       * @param {*}
       * @return {Promise<void>} - 无返回值
       */
      handleSearch() {
        this.pagination.currentPage = 1;
        this.searchForm.pageNumber = 1;
        this.loadData();
      },

      handleReset() {
        this.$refs.searchForm.resetFields();
        this.pagination.currentPage = 1;
        this.searchForm.pageNumber = 1;
        this.loadData();
      },

      handleAdd() {
        /**
         * @description 点击新增按钮事件处理函数
         * @param {*}  无参数
         * @return {void} - 无返回值
         * @description 根据当前 tabs  activeTab 的值，设置对应的 dialog  title 和 DialogForm 的状态
         */
        this.dialogVisible = true;
        if (this.activeTab === 'sensitive') {
          this.dialogStatus = 'create';
          this.currentDialogText = '新增敏感词';
        } else {
          this.dialogStatus = 'createFilters';
          this.currentDialogText = '新增过滤词';
        }
      },

      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.pagination.currentPage = 1;
        this.searchForm.pageSize = val;
        this.searchForm.pageNumber = 1;
        this.querySearch.pageSize = val;
        this.tablePagination.pageSize = val;
        this.tablePagination.currentPage = 1;
        this.loadData();
      },

      handleCurrentChange(val) {
        this.pagination.currentPage = val;
        this.searchForm.pageNumber = val;
        this.loadData();
      },

      /**
       * @description 查看敏感词或过滤词词条
       * @param {Object} row - 当前词表数据，包含名称和词条列表
       * @param {string} wordNameField - 敏感词或过滤词的名称字段
       * @returns {Promise<void>} - 无返回值
       * @description 根据当前 tabs  activeTab 的值，判断是敏感词还是过滤词，请求相应的 API，获取词条列表，打开查看词表 drawer
       */
      async viewWordList(row, wordNameField) {
        this.drawerMode = 'view';
        this.choooseRowViewWords = row;
        let arr = [];
        console.log('查看🚀🥶💩~ wordNameField', wordNameField);
        try {
          if (wordNameField === 'sensitiveWordName') {
            // this.querySearch.pageNum = 1;
            console.log('🚀🥶💩~ 1', 1);
            const { data } = await this.getSensitiveWordsItem(row.sensitiveWordCode);
            arr = Array.isArray(data.data) ? data.data : [];
            this.tableViewPagination.total = data.totalCount || 0;
            this.tableViewPagination.currentPage = 1;
          } else {
            console.log('🚀🥶💩~ 2', 2);
            // this.querySearch.pageNum = 1;
            const { data } = await this.getFilterWordsItem(row.filterWordCode, this.querySearch);
            arr = Array.isArray(data.data) ? data.data : [];
            this.tableViewPagination.total = data.totalCount || 0;
            this.tableViewPagination.currentPage = 1;
          }
          row.words = arr;
          this.currentWordList = { ...row };
          this.drawerVisible = true;

          this.tableViewPagination.total = parseInt(this.tableViewPagination.total);
          console.log('asdas 🚀🥶💩~ this.tableViewPagination', this.tableViewPagination);
          this.$refs.drawWordList.setPageData(this.tableViewPagination);
        } catch (error) {
          console.error('查看词表失败:', error);
          this.$message.error('查看词表失败');
        }
      },

      /**
       * 根据敏感词 code 获取敏感词词条
       * @param {string} sensitiveWordCode - 敏感词 code
       * @returns {Promise<Object>} - API 响应对象
       */
      async getSensitiveWordsItem(sensitiveWordCode) {
        this.querySearch.sensitiveWordCode = sensitiveWordCode;
        try {
          return await sensitiveWordsApi.getSensitiveWordsListApi(this.querySearch);
        } catch (error) {
          console.error('获取敏感词失败:', error);
          this.$message.error('获取敏感词失败');
          return { data: { data: [], totalCount: 0 } };
        }
      },

      /**
       * 根据过滤词 code 获取过滤词条
       * @param {string} filterWordCode - 过滤词 code
       * @param {Object} query - 查询参数对象，包含分页信息
       * @param {number} query.pageNum - 当前页码
       * @param {number} query.pageSize - 每页显示条目数量
       * @returns {Promise<Object>} - API 响应对象
       */

      /**
       * 根据过滤词 code 获取过滤词条
       * @param {string} filterWordCode - 过滤词 code
       * @param {Object} query - 查询参数对象，包含分页信息
       * @param {number} query.pageNum - 当前页码
       * @param {number} query.pageSize - 每页显示条目数量
       * @returns {Promise<Object>} - API 响应对象，如果失败返回一个空的数据对象
       */

      async getFilterWordsItem(filterWordCode, query) {
        try {
          return await sensitiveWordsApi.getFilterWordsListApi({
            FilterWordCode: filterWordCode,
            pageNum: query.pageNum,
            pageSize: query.pageSize
          });
        } catch (error) {
          console.error('获取过滤词失败:', error);
          this.$message.error('获取过滤词失败');
          return { data: { data: [], totalCount: 0 } };
        }
      },

      async copyFilterWords(filterWordId) {
        try {
          const { code } = await sensitiveWordsApi.copyFilterWords(filterWordId);
          return code === 20000;
        } catch (error) {
          console.error('复制过滤词失败:', error);
          this.$message.error('复制过滤词失败');
          return false;
        }
      },

      /**
       * @description 复制敏感词表
       * @param {string} sensitiveWordId - 敏感词表的 ID
       * @returns {Promise<boolean>} - 返回操作是否成功的布尔值
       */

      async copySensitiveWords(sensitiveWordId) {
        try {
          const { code } = await sensitiveWordsApi.copySensitiveWords(sensitiveWordId);
          return code === 20000;
        } catch (error) {
          console.error('复制敏感词失败:', error);
          this.$message.error('复制敏感词失败');
          return false;
        }
      },

      /**
       * @description 复制敏感词/过滤词表
       * @param {Object} row - 当前词表数据，包含名称和词条列表
       * @param {string} wordNumField - 敏感词/过滤词的数量字段
       * @returns {Promise<void>} - 无返回值
       * @description 根据当前 tabs  activeTab 的值，判断是敏感词还是过滤词，请求相应的 API，复制词表，打开确认框，确定后，刷新词表列表
       */
      async copyWordList(row, wordNumField) {
        const showTitle = this.activeTab === 'sensitive' ? '敏感词' : '过滤词';
        const showName = this.activeTab === 'sensitive' ? row.sensitiveWordName : row.filterWordName;
        this.$confirm(`是否复制${showTitle}表“ ${showName} ” ？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            let success = false;
            if (wordNumField === 'sensitiveWordNum') {
              success = await this.copySensitiveWords(row.id);
            } else {
              success = await this.copyFilterWords(row.id);
            }
            if (success) {
              this.$message.success('复制成功');
              await this.loadData();
            }
          })
          .catch(() => {
            this.$message.info('已取消复制');
          });
      },

      /**
       * @description 编辑敏感词/过滤词表
       * @param {Object} row - 当前词表数据，包含名称和词条列表
       * @param {string} wordNumField - 敏感词/过滤词的数量字段
       * @returns {Promise<void>} - 无返回值
       * @description 根据当前 tabs  activeTab 的值，判断是敏感词还是过滤词，请求相应的 API，获取词条列表，打开编辑词表抽屉
       * */
      async editWordList(row, wordNumField) {
        let arr = [];
        this.choooseRowWords = row;
        try {
          if (wordNumField === 'sensitiveWordNum') {
            // this.querySearch.pageNum = 1;
            const { data } = await this.getSensitiveWordsItem(row.sensitiveWordCode);
            arr = Array.isArray(data.data) ? data.data : [];
            this.tablePagination.total = data.totalCount || 0;
            this.tablePagination.currentPage = 1;
          } else {
            // this.querySearch.pageNum = 1;
            //调用过滤词分页接口
            const { data } = await this.getFilterWordsItem(row.filterWordCode, this.querySearch);
            arr = Array.isArray(data.data) ? data.data : [];
            this.tablePagination.total = data.totalCount || 0;
            this.tablePagination.currentPage = 1;
          }
          row.words = arr;
          this.currentWordList = { ...row };
          this.isEditing = true;
          this.addDrawerVisible = true;
          this.$refs.addWordList.changeCurrentPage();
          this.tablePagination.total = parseInt(this.tablePagination.total);
          this.$refs.addWordList.setPageData(this.tablePagination);
        } catch (error) {
          console.error('编辑词表失败:', error);
          this.$message.error('编辑词表失败');
        }
      },

      /**
       * @description 删除敏感词/过滤词表
       * @param {Object} row - 当前词表数据，包含词表的 ID
       * @returns {void} - 无返回值
       * @description 根据当前 tabs activeTab 的值，判断是敏感词还是过滤词，调用相应的 API 删除词表。
       * 在成功删除后显示成功消息并刷新词表列表；若删除失败，则显示错误消息。
       */

      deleteWordList(row) {
        this.$confirm('是否确认删除词表？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            if (this.activeTab === 'sensitive') {
              sensitiveWordsApi
                .deleteSensitiveWords(row.id)
                .then((res) => {
                  if (res.code === 20000) {
                    this.$message.success('删除成功');
                    this.loadData();
                  } else {
                    this.$message.error('删除失败');
                  }
                })
                .catch(() => this.$message.info('已取消删除'));
            } else {
              sensitiveWordsApi
                .deleteFilterWords(row.id)
                .then((res) => {
                  if (res.code === 20000) {
                    this.$message.success('删除成功');
                    this.loadData();
                  } else {
                    this.$message.error('删除失败');
                  }
                })
                .catch(() => this.$message.info('已取消删除'));
            }
          })
          .catch(() => {
            this.$message.info('已取消删除');
          });
      },

      /**
       * @description 保存词表，编辑词表时触发
       * @param {Object} updatedItem - 编辑后的词表数据，包含词表名称和词条列表
       * @returns {void} - 无返回值
       * @description 保存词表，设置词表的最后更新人和更新时间，根据词表的 ID 在 allData 中查找并更新，若不存在则添加新词表，最后刷新词表列表
       */
      handleSaveWordList(updatedItem) {
        updatedItem.lastModifier = this.currentUser;
        updatedItem.updateTime = new Date().toLocaleString();
        const index = this.allData.findIndex((item) => item.id === updatedItem.id);
        if (index !== -1) {
          this.$set(this.allData, index, updatedItem);
        } else {
          updatedItem.id = this.allData.length + 1;
          updatedItem.creator = this.currentUser;
          this.allData.push(updatedItem);
        }
        this.loadData();
        this.$message.success('保存成功');
      },

      /**
       * @description 加载更多词条，用于查看词表和编辑词表的抽屉
       * @param {Function} callback - 回调函数，传入无参数
       * @returns {Promise<void>} - 无返回值
       * @description 该函数将检查当前是否在加载状态下，如果是，则跳过本次加载，否则将请求下一页的数据，合并到当前词表的词条列表中，最后刷新词表列表
       * 该函数将在请求失败时，显示错误信息，并将当前页码减1，以便下次继续加载
       * 该函数将在请求成功时，刷新当前词表的词条列表，并将当前页码设置为最新的页码
       */
      async loadMore(callback) {
        if (this.loading) {
          console.log('加载中，跳过 loadMore');
          callback();
          return;
        }
        this.loading = true;
        this.querySearch.pageNum += 1;

        console.log(`loadMore: 请求第 ${this.querySearch.pageNum} 页, total=${this.tablePagination.total}`);

        // 检查是否超过总页数
        const totalPages = Math.ceil(this.tablePagination.total / this.querySearch.pageSize);
        if (this.querySearch.pageNum > totalPages && this.tablePagination.total > 0) {
          console.log('已到达最后一页，停止加载');
          this.$message.info('没有更多数据');
          this.querySearch.pageNum -= 1;
          this.loading = false;
          this.$set(this.tablePagination, 'currentPage', totalPages);
          callback();
          return;
        }

        try {
          let newData = [];
          if (this.activeTab === 'sensitive') {
            console.log('请求敏感词数据:', this.querySearch);
            const res = await sensitiveWordsApi.getSensitiveWordsListApi(this.querySearch);
            console.log('敏感词响应:', res);
            newData = Array.isArray(res.data?.data) ? res.data.data : [];
            this.tablePagination.total = res.data?.totalCount || 0;
          } else {
            console.log('请求过滤词数据:', this.currentWordList.filterWordCode, this.querySearch);
            const res = await this.getFilterWordsItem(this.currentWordList.filterWordCode, this.querySearch);
            console.log('过滤词响应:', res);
            newData = Array.isArray(res.data?.data) ? res.data.data : [];
            this.tablePagination.total = res.data?.totalCount || 0;
          }

          console.log(`收到数据: ${newData.length} 条`);
          if (newData.length > 0) {
            this.$set(this.currentWordList, 'words', [...this.currentWordList.words, ...newData]);
            this.$set(this.tablePagination, 'currentPage', this.querySearch.pageNum);
            console.log('10098🚀🥶💩~ this.currentWordList', this.currentWordList);
          } else {
            console.log('无新数据，停止加载');
            this.$message.info('没有更多数据');
            this.querySearch.pageNum -= 1;
          }
        } catch (err) {
          console.error('加载失败:', err);
          this.$message.error(`加载失败: ${err.message || '未知错误'}`);
          this.querySearch.pageNum -= 1;
        } finally {
          this.loading = false;
          console.log('loadMore 完成, 当前页:', this.tablePagination.currentPage);
          callback();
        }
      },

      /**
       * @description 保存敏感词/过滤词表
       * @param {Object} updatedItem - 保存后的词表数据，包含词表名称和词条列表
       * @returns {void} - 无返回值
       * @description 保存敏感词/过滤词表，设置词表的最后更新人和更新时间，根据词表的 ID 在 allData 中查找并更新，若不存在则添加新词表，最后刷新词表列表
       */
      handleSaveNewWordList(updatedItem) {
        updatedItem.lastModifier = this.currentUser;
        updatedItem.updateTime = new Date().toLocaleString();
        const index = this.allData.findIndex((item) => item.id === updatedItem.id);
        if (index !== -1) {
          this.$set(this.allData, index, updatedItem);
        } else {
          updatedItem.id = this.allData.length + 1;
          updatedItem.creator = this.currentUser;
          this.allData.push(updatedItem);
        }
        this.loadData();
        this.$message.success('保存成功');
      },

      /**
       * @description 编辑成功后刷新词表
       * @param {Object} currentWordList - 当前词表数据，包含词表名称和词条列表
       * @param {string} val - 词表类型，'sensitive' 或 'filter'
       * @returns {Promise<void>} - 无返回值
       * @description 编辑成功后，根据词表的 ID 请求词条数据，更新词表的词条数据，最后刷新词表列表
       */
      async editSuccess(currentWordList, val) {
        let arr = [];
        try {
          if (val !== 'filter') {
            this.querySearch.pageNum = 1;
            const { data } = await this.getSensitiveWordsItem(currentWordList.sensitiveWordCode);
            arr = Array.isArray(data.data) ? data.data : [];
            this.tablePagination.total = data.totalCount || 0;
            this.tablePagination.currentPage = 1;
          } else {
            this.querySearch.pageNum = 1;
            const { data } = await this.getFilterWordsItem(currentWordList.filterWordCode, this.querySearch);
            arr = Array.isArray(data.data) ? data.data : [];
            this.tablePagination.total = data.totalCount || 0;
            this.tablePagination.currentPage = 1;
          }
          currentWordList.words = arr;
          this.currentWordList = { ...currentWordList };
          await this.loadData();
        } catch (error) {
          console.error('编辑成功后刷新失败:', error);
          this.$message.error('刷新数据失败');
        }
      },

      handleDrawerClose() {
        this.drawerVisible = false;
        this.currentWordList = {};
        this.tablePagination.currentPage = 1;
        this.querySearch.pageNum = 1;
        this.querySearch.pageSize = 10;
        this.$refs.drawWordList.setPageData(this.querySearch);
      },

      handleAddDrawerClose() {
        this.addDrawerVisible = false;
        this.currentWordList = {};
        this.tablePagination.currentPage = 1;
        this.querySearch.pageNum = 1;
        this.querySearch.pageSize = 10;
        this.$refs.drawWordList.setPageData(this.tablePagination);
      },

      canEdit(row) {
        return this.userRole === 'admin' || row.creator === this.currentUser;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .no-more {
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .el-table {
    border: 1px solid #ebeef5;
  }

  .el-table th,
  .el-table td {
    border-right: none;
  }

  .pagination-container {
    padding: 20px 0;
    background-color: #fff;
  }

  .rule-name {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    color: #409eff;
  }

  .rule-name:hover {
    text-decoration: underline;
  }
</style>
