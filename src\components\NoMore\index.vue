<template>
  <div class="nomore">
    <el-image style="width: 100px; height: 100px" :src="image" />
    <div style="color: #999; margin-top: 20px">{{ text }}</div>
  </div>
</template>

<script>
  export default {
    name: '<PERSON>er',
    props: {
      image: {
        type: String,
        default: 'https://document.dxznjy.com/automation/1728442200000'
      },
      text: {
        type: String,
        default: '暂无数据'
      }
    }
  };
</script>

<style scoped>
  .nomore {
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
