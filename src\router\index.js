import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

/* Layout */
import Layout from '@/layout';
import chatRouter from './modules/chat';
import systemRouter from './modules/system';
import historyLog from '@/router/modules/historyLog';
import roleRelationshipManagement from '@/router/modules/roleRelationshipManagement';
import riskAudit from '@/router/modules/riskAudit';
import toolRouter from '@/router/modules/toolView';
/* Router Modules */
export const constantRouterMap = [
  { path: '/', redirect: '/Home' },
  // { path: '/', redirect: '/chatContent/chatFile' },
  {
    path: '',
    component: Layout,
    name: 'Home',

    children: [
      {
        path: 'Home',
        component: () => import('@/views/Home'),
        name: 'Home',
        meta: {
          title: 'IM中台',
          icon: 'dashboard',
          noCache: true
        }
      }
    ]
  },
  {
    path: '/forgot',
    component: () => import('@/views/forgot'),
    name: 'forgot'
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  }
];

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRouterMap = [
  // 404 page must be placed at the end !!!
  chatRouter,
  riskAudit,
  roleRelationshipManagement,
  toolRouter,
  systemRouter,
  historyLog,
  { path: '*', redirect: '/404', hidden: true }
];

export default new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRouterMap
});
