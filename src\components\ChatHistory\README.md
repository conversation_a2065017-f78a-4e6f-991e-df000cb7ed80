# ChatHistory 组件使用文档

## 组件介绍

ChatHistory 是一个聊天记录展示组件，支持多种消息类型的显示、搜索、筛选和分页加载功能。

## 功能特性

- 支持多种消息类型（文本、图片、文件、视频、语音、位置、表情、自定义消息、合并转发）
- 支持消息搜索
- 支持按发送人筛选
- 支持按日期筛选
- 支持分页加载
- 支持合并转发消息的折叠/展开
- 支持消息预览和详情查看

## 基本用法

```vue
<template>
  <chat-history :message-list="messageList" :user-list="userList" :self="self" @filter-change="handleFilterChange" @load-more="handleLoadMore" />
</template>

<script>
  export default {
    data() {
      return {
        messageList: [], // 消息列表
        userList: [], // 用户列表
        self: {
          // 当前用户信息
          id: 'currentUserId'
        }
      };
    },
    methods: {
      // 处理筛选条件变化
      async handleFilterChange(filterParams) {
        const { content, senderId, times } = filterParams;
        // 调用接口获取筛选后的消息
        const res = await this.$api.getChatHistory({
          content,
          senderId,
          times
        });
        this.messageList = res.data;
      },

      // 处理加载更多
      async handleLoadMore() {
        // 调用接口获取更多消息
        const res = await this.$api.getMoreChatHistory();
        this.messageList = [...this.messageList, ...res.data];
      }
    }
  };
</script>
```

## Props

| 参数        | 说明         | 类型   | 默认值 |
| ----------- | ------------ | ------ | ------ |
| messageList | 消息列表     | Array  | []     |
| userList    | 用户列表     | Array  | []     |
| self        | 当前用户信息 | Object | {}     |

## Events

| 事件名        | 说明                     | 回调参数                                             |
| ------------- | ------------------------ | ---------------------------------------------------- |
| filter-change | 筛选条件变化时触发       | { content: string, senderId: string, times: string } |
| load-more     | 滚动到底部加载更多时触发 | -                                                    |

## 消息类型说明

组件支持以下消息类型：

1. **TIMTextElem** - 文本消息
2. **TIMImageElem** - 图片消息
3. **TIMFileElem** - 文件消息
4. **TIMVideoFileElem** - 视频消息
5. **TIMSoundElem** - 语音消息
6. **TIMLocationElem** - 位置消息
7. **TIMFaceElem** - 表情消息
8. **TIMCustomElem** - 自定义消息
9. **TIMRelayElem** - 合并转发消息

## 消息数据结构

```javascript
// 基本消息结构
{
  id: string,              // 消息ID
  messageType: string,     // 消息类型
  messageContent: string,  // 消息内容
  createTime: string,      // 创建时间
  senderId: string,        // 发送者ID
  nickname: string,        // 发送者昵称
  senderRoleName: string,  // 发送者角色名称
  receiverId: string       // 接收者ID
}

// 文件消息额外字段
{
  fileName: string,        // 文件名
  fileSize: number         // 文件大小
}

// 合并转发消息额外字段
{
  messageContent: {
    title: string,         // 转发标题
    details: Array<{       // 转发消息列表
      nickName: string,    // 发送者昵称
      messageType: string, // 消息类型
      content: string      // 消息内容
    }>
  }
}
```

## 样式定制

组件使用 SCSS 编写样式，可以通过以下类名进行样式覆盖：

- `.chat-history-container` - 容器样式
- `.search-filter-section` - 搜索和筛选区域
- `.chat-list` - 消息列表
- `.message-group` - 消息组
- `.message-item` - 单条消息
- `.message-content` - 消息内容
- `.relay-message` - 合并转发消息

## 注意事项

1. 消息列表需要按时间倒序排列
2. 分页加载时注意处理重复消息
3. 合并转发消息支持折叠/展开功能
4. 文件消息需要处理文件大小格式化
5. 自定义消息需要处理 JSON 解析
