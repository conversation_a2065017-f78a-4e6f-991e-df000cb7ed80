/** * 聊天记录详情抽屉组件 * @component DrawContent * @description 用于显示投诉相关的聊天记录详情，包含聊天记录列表、用户信息、处理结果等功能 */
<template>
  <el-drawer :before-close="handleClose" :direction="direction" :visible.sync="drawer" :with-header="false" size="60%" title="记录详情">
    <div class="chat-record-container">
      <!-- 头部信息 -->
      <div class="header-title" style="display: flex">
        <h2>记录详情</h2>
      </div>
      <div class="icon-box">
        <i class="el-icon-refresh-right" style="font-size: 24px" @click="resetBtn" />
        <i class="el-icon-close" style="font-size: 30px; margin-left: 24px; margin-right: 12px" @click="handleClose" />
      </div>
    </div>

    <div class="main-content">
      <div v-loading="chatLoading" class="chat-list">
        <div class="message-type-tabs">
          <el-radio-group v-model="activeType" @change="changeTab">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="files">文件</el-radio-button>
          </el-radio-group>
        </div>
        <ChatHistory
          ref="chatHistoryRef"
          :distance="10"
          :messageList="messageList"
          :self="selectedUser"
          :userList="userArr"
          class="chat-history"
          @filterChange="filterChangeChatList"
          @loadMore="loadMoreChatList"
        ></ChatHistory>
      </div>
      <div class="info-section">
        <el-form class="description-form" label-position="left" label-width="80px">
          <h3>基本信息</h3>
          <el-form-item label="触发规则">
            <span>{{ getInitData.ruleName || '' }}</span>
          </el-form-item>
          <el-form-item label="风险内容">
            <span>{{ getInitData.riskContent || '' }}</span>
          </el-form-item>
          <el-form-item label="发送人">
            <span>{{ getInitData.senderName || '' }}</span>
          </el-form-item>
          <el-form-item label="接收人">
            <span>{{ getInitData.receiverName || '' }}</span>
          </el-form-item>
          <el-form-item label="发送时间">
            <span>{{ getInitData.messageTime || '' }}</span>
          </el-form-item>
          <el-button size="mini" style="margin-left: 4px" type="primary" @click="isViewChat = !isViewChat">
            {{ isViewChat ? '关闭触发敏感词聊天' : '查看触发敏感词聊天' }}
          </el-button>
          <div v-if="complaintList.length > 0 && isViewChat" class="complaint-list">
            <ShowComplaint :messageList="complaintList" :self="selectedUser" />
          </div>
        </el-form>
      </div>
    </div>
  </el-drawer>
</template>

<script>
  import ChatHistory from '@/components/ChatHistory/index.vue';
  import NoMore from '@/components/NoMore/index.vue';
  import { getChatListGroup, getChatListSingle, getFileList, getGroupUserList } from '@/api/chatContent/chatFile';
  import riskSetting from '@/api/riskAudit/riskSetting';
  import riskRecordApi from '@/api/riskAudit/riskRecord';
  import ShowComplaint from '@/views/riskAudit/complaintManage/components/ShowComplaint.vue';

  export default {
    name: 'DrawContent',
    components: { ShowComplaint, NoMore, ChatHistory },
    data() {
      return {
        /**
         * 处理结果文本内容
         * @type {string}
         */
        searchQuery: '',
        /**
         * 选择的类型
         * @type {string}
         */
        chooseType: '1',
        /**
         * 聊天用户数据
         * @type {Object}
         * @property {string} id - 用户ID
         */
        chatUserData: {
          id: ''
        },

        /**
         * 选中的用户信息
         * @type {Object}
         */
        selectedUser: {},
        /**
         * 当前激活的消息类型
         * @type {string}
         */
        activeType: 'all',
        /**
         * 消息列表
         * @type {Array}
         */
        messageList: [],
        /**
         * 消息总数
         * @type {number}
         */
        messageTotal: 0,
        /**
         * 初始化数据
         * @type {Object}
         */
        getInitData: {},
        /**
         * 抽屉组件显示状态
         * @type {boolean}
         */
        drawer: false,
        /**
         * 抽屉组件方向
         * @type {string}
         */
        direction: 'rtl',

        /**
         * 接收者ID
         * @type {string}
         */
        receiverId: '',
        /**
         * 发送者ID
         * @type {string}
         */
        senderId: '',
        /**
         * 聊天类型
         * @type {string}
         */
        chatType: 'single',
        /**
         * 单聊查询参数
         * @type {Object}
         * @property {string} senderId - 发送者ID
         * @property {string} receiverId - 接收者ID
         * @property {string} content - 内容
         * @property {string} times - 时间
         * @property {number} pageNum - 页码
         * @property {number} pageSize - 每页数量
         */
        singleQuery: {
          senderId: '',
          receiverId: '',
          content: '',
          times: '',
          pageNum: 1,
          pageSize: 10
        },
        /**
         * 群聊查询参数
         * @type {Object}
         * @property {string} senderId - 发送者ID
         * @property {string} groupId - 群组ID
         * @property {string} content - 内容
         * @property {string} times - 时间
         * @property {number} pageNum - 页码
         * @property {number} pageSize - 每页数量
         */
        groupQuery: {
          senderId: '',
          groupId: sessionStorage.getItem('riskGroupId'),
          content: '',
          times: '',
          pageNum: 1,
          pageSize: 10
        },
        /**
         * 用户列表
         * @type {Array}
         */
        userArr: [],
        allMessage: [],
        singleGroupData: {},
        isViewChat: false,
        complaintList: [],
        singleFileQuery: {
          senderId: '',
          receiverId: '',
          fileName: '',
          fileType: '',
          times: '',
          sender: '',
          pageNum: 1,
          pageSize: 10,
          chatType: 1
        },
        groupFileQuery: {
          fileName: '',
          fileType: '',
          times: '',
          sender: '',
          groupId: sessionStorage.getItem('riskGroupId'),
          pageNum: 1,
          pageSize: 10,
          chatType: 2
        },
        chatLoading: false
      };
    },
    methods: {
      /**
       * 过滤聊天列表
       * @param {Object} data - 过滤条件
       */

      async filterChangeChatList(data) {
        this.groupQuery.pageNum = 1;
        let type = '';
        if (this.messageList?.length === 0) {
          let messageItem = JSON.parse(sessionStorage.getItem('riskMessageItem'));
          type = messageItem.chatType === '1' ? 'single' : 'group';
        } else {
          type = this.messageList?.[0].chatType === '1' ? 'single' : 'group';
        }
        this.messageList = [];
        this.messageTotal = 0;
        let res = '';
        if (type === 'single' && this.activeType === 'all') {
          this.singleQuery.pageNum = 1;
          this.singleQuery.sender = data.senderId;
          this.singleQuery.times = data.times;
          this.singleQuery.content = data.content;
          res = await getChatListSingle(this.singleQuery);
          console.log('单聊全部怒🚀🥶💩~ res', res);
          this.pubListMethod(res);
        } else if (type === 'single' && this.activeType === 'files') {
          this.singleFileQuery.pageNum = 1;
          this.singleFileQuery.fileName = data.content;
          this.singleFileQuery.sender = data.senderId;
          this.singleFileQuery.fileType = data.fileType;
          this.singleFileQuery.times = data.times;
          this.singleFileQuery.chatType = 1;
          res = await getFileList(this.singleFileQuery);
          this.pubListMethod(res);
        } else if (type === 'group' && this.activeType === 'all') {
          this.groupQuery.content = data.content;
          this.groupQuery.times = data.times;
          this.groupQuery.sender = data.senderId;
          res = await getChatListGroup(this.groupQuery);
          console.log('群聊全部怒🚀🥶💩~ res', res);
          this.pubListMethod(res);
        } else {
          this.groupFileQuery.pageNum = 1;
          this.groupFileQuery.fileName = data.content;
          this.groupFileQuery.sender = data.senderId;
          this.groupFileQuery.fileType = data.fileType;
          this.groupFileQuery.times = data.times;
          this.groupFileQuery.chatType = 2;
          res = await getFileList(this.groupFileQuery);
          this.pubListMethod(res);
        }
      },

      /**
       * 切换标签页
       * @param {string} e - 标签页类型
       */
      async changeTab(e) {
        this.singleQuery.content = '';
        this.singleQuery.times = '';
        this.singleQuery.pageNum = 1;
        this.groupQuery.pageNum = 1;
        this.$refs.chatHistoryRef.filterDate = '';
        this.$refs.chatHistoryRef.filterSender = '';
        this.$refs.chatHistoryRef.searchKey = '';
        this.$refs.chatHistoryRef.deleteHlightedMessages();
        this.messageList = []; // 切换tab类型时应该将消息列表置空
        this.messageTotal = 0;
        const messageItem = JSON.parse(sessionStorage.getItem('riskMessageItem'));
        if (messageItem.chatType === '1' && this.activeType === 'all') {
          this.singleQuery.content = '';
          await this.getSingleMessage();
        } else if (messageItem.chatType === '1' && this.activeType === 'files') {
          this.singleFileQuery.chatType = 1;
          this.singleFileQuery.pageNum = 1;
          this.singleFileQuery.fileName = '';
          this.singleFileQuery.senderId = String(this.userArr[0].tencentId);
          this.singleFileQuery.receiverId = String(this.userArr[1].tencentId);
          const res = await getFileList(this.singleFileQuery);
          this.pubListMethod(res);
        } else if (messageItem.chatType === '2' && this.activeType === 'all') {
          console.log('🚀🥶💩~ 群聊且全部信息');
          console.log('🚀🥶💩~ this.groupQuery', this.groupQuery);
          this.groupQuery.content = '';
          this.groupQuery.groupId = sessionStorage.getItem('riskGroupId');
          console.log('111群聊🚀🥶💩~ this.groupQuery', this.groupQuery);

          const res2 = await getChatListGroup(this.groupQuery);
          this.pubListMethod(res2);
        } else {
          console.log('🚀🥶💩~ 群聊且文件信息');
          console.log('🚀🥶💩~ this.groupFileQuery', this.groupFileQuery);
          this.groupFileQuery.fileName = '';
          this.groupFileQuery.pageNum = 1;
          this.groupFileQuery.chatType = 2;
          this.groupFileQuery.groupId = sessionStorage.getItem('riskGroupId');
          const res3 = await getFileList(this.groupFileQuery);
          this.pubListMethod(res3);
        }
      },

      /**
       * 初始化文件列表
       * @param {Object} item - 用户/群组信息
       * @param {String} type - 聊天类型(single/group)
       */
      async initFileList(item, type) {
        let res;
        this.chatLoading = true;
        res = await getFileList(item);
        this.messageTotal = +res.data.totalItems;
        if (res.data.data.length === 0) {
          this.messageList = [];
        } else {
          let arr = this.messageList.concat(res.data.data);
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType == 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
        }

        this.chatLoading = false;
      },

      /**
       * 数据公共处理方法
       * @param res
       */
      pubListMethod(res) {
        if (res.code === 20000) {
          this.messageTotal = +res.data.totalItems;
          let arr = this.messageList.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.messageList = [];
            this.chatLoading = false;
            return;
          }
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType == 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
          this.chatLoading = false;
        }
      },

      /**
       * 加载更多聊天记录
       * @description 当滚动到底部时加载更多聊天记录
       */
      async loadMoreChatList() {
        if (this.chatLoading) return;
        console.log('🚀🥶💩~ this.messageList', this.messageList);
        console.log('🚀🥶💩~ this.messageTotal', this.messageTotal);
        if (this.messageList.length === this.messageTotal) return;
        this.chatLoading = true;
        if (this.messageList.length < this.messageTotal) {
          let messageItem = JSON.parse(sessionStorage.getItem('riskMessageItem'));
          let res = '';
          if (messageItem.chatType === '1' && this.activeType === 'all') {
            // 1.单聊且全部聊天信息
            console.log('单聊且全部聊天信息🚀🥶💩~ this.singleQuery', this.singleQuery);
            this.singleQuery.pageNum++;
            res = await getChatListSingle(this.singleQuery);
            this.pubListMethod(res);
          } else if (messageItem.chatType === '1' && this.activeType === 'files') {
            // 2.单聊且文件信息
            this.singleFileQuery.senderId = this.userArr[0].tencentId;
            this.singleFileQuery.receiverId = this.userArr[1].tencentId;
            this.singleFileQuery.chatType = 1;
            console.log('单聊且文件信息🚀🥶💩~ this.singleFileQuery', this.singleFileQuery);
            this.singleFileQuery.pageNum++;
            res = await getFileList(this.singleFileQuery);
            this.pubListMethod(res);
          } else if (messageItem.chatType === '2' && this.activeType === 'all') {
            // 3.群聊且全部聊天信息
            console.log('群聊且全部聊天信息🚀🥶💩~ this.groupQuery', this.groupQuery);
            this.groupQuery.pageNum++;
            console.log('🚀🥶💩~ this.groupQuery', this.groupQuery);
            res = await getChatListGroup(this.groupQuery);
            this.pubListMethod(res);
          } else {
            // 4.群聊且文件信息
            console.log('群聊且文件信息🚀🥶💩~ this.groupFileQuery', this.groupFileQuery);
            this.groupFileQuery.pageNum++;
            this.groupFileQuery.chatType = 2;
            res = await getFileList(this.groupFileQuery);
            this.pubListMethod(res);
          }
          this.chatLoading = false;
        }
        this.chatLoading = false;
      },

      /**
       * 关闭抽屉
       */
      handleClose() {
        this.drawer = false;
        this.singleQuery.content = '';
        this.singleQuery.times = '';
        this.singleQuery.pageNum = 1;
        this.$refs.chatHistoryRef.filterDate = '';
        this.$refs.chatHistoryRef.filterSender = '';
        this.$refs.chatHistoryRef.searchKey = '';
        this.messageList = [];
        this.complaintList = [];
        this.activeType = 'all';
        this.isViewChat = false;
        this.resetSearchParams();
      },

      /**
       * 重置聊天记录搜索条件
       */
      resetBtn() {
        this.activeType = 'all';
        this.resetSearchParams();
        this.messageList = [];
        this.getGroupMessage(); // 调用获取聊天记录
      },

      //  重置搜索参数
      resetSearchParams() {
        this.$refs.chatHistoryRef.filterDate = '';
        this.$refs.chatHistoryRef.filterSender = '';
        this.$refs.chatHistoryRef.searchKey = '';
        // 单聊
        this.singleQuery.content = '';
        this.singleQuery.sender = '';
        this.singleQuery.times = '';
        this.singleQuery.pageNum = 1;
        this.singleQuery.pageSize = 10;
        // 群聊
        this.groupQuery.content = '';
        this.groupQuery.times = '';
        this.groupQuery.sender = '';
        this.groupQuery.pageNum = 1;
        this.groupQuery.pageSize = 10;
        // 单聊文件
        this.singleFileQuery = {
          content: '',
          times: '',
          sender: '',
          pageNum: 1,
          pageSize: 10
        };
        //  群聊文件记录
        this.groupFileQuery = {
          content: '',
          times: '',
          sender: '',
          pageNum: 1,
          pageSize: 10
        };
      },

      /**
       * 获取群成员信息
       * @returns {Promise<void>}
       */
      async getGroupMember() {
        const groupId = sessionStorage.getItem('riskGroupId');
        let { data } = await getGroupUserList({
          groupId: groupId,
          pageNum: 1,
          pageSize: 100
        });
        this.userArr = data;
      },

      /**
       * 获取初始化数据
       * @param row
       * @param messageId
       */
      async getMessage(row, messageId) {
        this.getInitData = row;
        this.groupQuery.pageNum = 1;

        try {
          this.chatLoading = true;
          const { code, data } = await riskRecordApi.getChatRecord({ messageId });
          if (code === 20000) {
            if (data.length === 0) {
              this.$message.warning('聊天消息正在处理中，请稍后~');
              return;
            }
            this.complaintList = data || [];
            console.log('🚀🥶💩~ this.complaintList', this.complaintList);
            this.complaintList.forEach((item) => {
              if (item.messageType == 'TIMRelayElem') {
                let obj;
                obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
                item.messageContent = obj;
              }
            });

            if (this.complaintList.length === 0) return;

            let messageItem = data[0];

            sessionStorage.setItem('riskGroupId', messageItem.groupId || '');
            console.log('===============🚀🥶💩~ messageItem.groupId', messageItem.groupId);
            sessionStorage.setItem('riskMessageItem', JSON.stringify(messageItem));
            console.log('🚀🥶💩~ 222222222', 2222222222222);

            if (messageItem.chatType === '2') {
              console.log('🚀🥶💩~是群聊+ 获取群聊成员信息');
              await this.getGroupMember();
            }
            this.chatType = data[0].chatType === '1' ? 'single' : 'groupChat'; // 1单聊 2 群聊
            if (this.chatType === 'single') {
              await this.getSinglePerson(messageItem);
              await this.getSingleMessage();
              return;
            }
            if (messageItem) {
              console.log('🚀🥶💩~ 111111111111', 111111111111);
              console.log('🚀🥶💩~ 执行前', 111111111111);
              this.chatLoading = false;
              await this.getGroupMessage(messageItem);
            }
          }
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
          this.messageList = [];
        } finally {
          this.chatLoading = false;
        }
      },

      // 获取单聊聊天记录
      async getSingleMessage() {
        this.chatLoading = true;
        this.singleQuery.senderId = String(this.userArr[0].tencentId);
        this.singleQuery.receiverId = String(this.userArr[1].tencentId);
        console.log('获取单聊聊天记录🚀🥶💩~ this.singleQuery', this.singleQuery);
        const res = await getChatListSingle(this.singleQuery);
        this.messageTotal = +res.data.totalItems;
        if (res.data.data.length === 0) {
          this.messageList = [];
        } else {
          let arr = this.messageList.concat(res.data.data);
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType == 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
        }
        this.chatLoading = false;
      },

      /**
     /**
     * 获取群聊信息
     */
      async getGroupMessage() {
        console.log('🚀🥶💩~ 执行了吗');
        console.log('🚀🥶💩~ this.chatLoading', this.chatLoading);
        if (this.chatLoading) return;
        this.chatLoading = true;
        let groupId = sessionStorage.getItem('riskGroupId');
        console.log('000000🚀🥶💩~ groupId', groupId);
        let { data } = await getGroupUserList({
          groupId: groupId,
          pageNum: 1,
          pageSize: 100
        });
        this.userArr = data;
        this.groupQuery.groupId = groupId;
        console.log('11111群聊🚀🥶💩~ this.groupQuery', this.groupQuery);
        const res = await getChatListGroup(this.groupQuery);
        console.log('获取群聊消息🚀🥶💩~ res', res);
        this.messageTotal = +res.data.totalItems;
        if (res.data.data.length === 0) {
          this.messageList = [];
        } else {
          let arr = this.messageList.concat(res.data.data);
          this.messageList = JSON.parse(JSON.stringify(arr));
          this.messageList.forEach((item) => {
            if (item.messageType == 'TIMRelayElem') {
              let obj;
              obj = typeof item.messageContent == 'string' ? JSON.parse(item.messageContent).layInfo : item.messageContent;
              item.messageContent = obj;
            }
          });
        }
        this.chatLoading = false;
      },

      /**
       * 获取单聊用户信息
       * @param row
       * @returns {Promise<void>}
       */
      async getSinglePerson(row) {
        let params = {
          tenantIds: [row.receiverId, row.senderId]
        };
        try {
          const { code, data } = await riskSetting.getChatUserListSingle(params);
          if (code === 20000) {
            this.userArr = [
              {
                nickname: data[0].nickname,
                tencentId: data[0].tencentId,
                role: data[0].role
              },
              {
                nickname: data[1].nickname,
                tencentId: data[1].tencentId,
                role: data[1].role
              }
            ];

            // 存储聊天人员的信息
            this.singleGroupData = {
              senderId: data[0].tencentId,
              receiverId: data[1].tencentId
            };
          }
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .chat-record-container {
    padding: 10px 20px;
    margin: 0 auto;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    display: flex;
    justify-content: space-between;
    align-content: center;
    border-bottom: 1px solid #bbbbbb;

    .header-title {
      width: 60%;
      display: flex;
      justify-content: space-between;

      h2 {
        font-size: 18px;
        color: #333;
        margin-bottom: 15px;
        width: 15%;
      }

      .header {
        display: flex;
        width: 90%;

        .user-info {
          display: flex;
          align-items: center;
          font-size: 24px;
          justify-content: center;
          margin: 0 auto;

          .chat-title,
          .chat-subtitle {
            color: #000000;
            font-size: 20px;
          }
        }
      }
    }

    .icon-box {
      display: flex;
      align-items: center;
    }
  }

  .main-content {
    display: flex;
    height: calc(100vh - 84px);
    position: relative;

    .chat-list {
      width: 60%;
      border-right: 1px solid #bbbbbb;
      height: calc(100vh - 84px);
      overflow-y: auto;

      .message-type-tabs {
        padding: 6px 24px;
        background: #fff;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: center;

        .el-radio-group {
          width: 240px;

          :deep(.el-radio-button__inner) {
            padding: 8px 0;
            font-size: 13px;
            border-color: #dcdfe6;

            &:hover {
              color: #409eff;
            }
          }

          :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
            background-color: #409eff;
            border-color: #409eff;
            box-shadow: -1px 0 0 0 #409eff;
          }
        }
      }

      .top {
        margin: 10px;
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }

    .info-section {
      width: 40%;
      padding: 20px;

      .description-form {
        .el-form-item {
          margin-bottom: 15px;
        }

        .el-form-item__label {
          font-weight: bold;
          color: #606266;
        }

        .el-form-item__content {
          color: #909399;
        }
      }
    }
  }
</style>
