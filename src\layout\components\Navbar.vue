<template>
  <el-menu class="navbar" mode="horizontal">
    <hamburger class="hamburger-container" :toggleClick="toggleSideBar" :isActive="true"></hamburger>

    <breadcrumb class="breadcrumb-container"></breadcrumb>
    <div class="right-menu" :style="this.screenUWidth < 460 ? 'fontSize:12px;' : 'fontSize:16px;'">
      <div style="display: inline-block; float: left">
        <span>帐号：</span>
        <el-tag style="margin-right: 10px">{{ name }}</el-tag>
      </div>
      <el-dropdown class="avatar-container right-menu-item" :style="this.screenUWidth < 360 ? 'width:50px;' : ''" trigger="click">
        <div class="avatar-wrapper" style="margin-top: 0">
          <img class="user-avatar" :src="avatar" :style="this.screenUWidth < 360 ? 'height:30px;margin-top:8px' : 'height:37px;margin-top:8px'" />
          <i class="el-icon-caret-bottom"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/">
            <el-dropdown-item>首页</el-dropdown-item>
          </router-link>
          <el-dropdown-item>
            <span @click="handleUpdatePwd" style="display: block">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item divided>
            <span @click="logout" style="display: block">退出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <!--弹出窗口：修改密码-->
    <el-dialog title="修改密码" :visible.sync="dialogVisible" width="70%">
      <el-form :rules="rules" ref="dataForm" :model="temp" label-position="left" label-width="120px">
        <el-form-item label="手机号" prop="mobile" style="width: 40%">
          <el-input v-model="temp.mobile" placeholder="请输入手机号" disabled></el-input>
        </el-form-item>

        <el-form-item label="密码" prop="newPwd" style="width: 40%">
          <el-input type="password" v-model="temp.newPwd" placeholder="请输入密码"></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPwd" style="width: 40%">
          <el-input type="password" v-model="temp.confirmPwd" placeholder="请再次输入密码"></el-input>
        </el-form-item>

        <el-form-item label="验证码" prop="smsCode" style="width: 40%">
          <div style="display: flex; justify-content: space-between">
            <el-input placeholder="请输入验证码" v-model="temp.smsCode" style="display: inline-block"></el-input>
            <el-button @click.stop="getSmsClick()" :disabled="disabledSmsClick" style="display: inline-block; margin-left: 50px">{{ countdown }}</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="quickBtn">取消</el-button>
        <el-button type="primary" @click="updatePwd">确定</el-button>
      </div>
    </el-dialog>
    <Verify ref="verify" :captcha-type="'blockPuzzle'" :img-size="{ width: '375px', height: '200px' }" @success="sendSmg" @closeSend="closeSend" style="z-index: 9999 !important" />
  </el-menu>
</template>

<script>
  import { mapGetters } from 'vuex';
  import Breadcrumb from '@/components/Breadcrumb';
  import Hamburger from '@/components/Hamburger';
  import Verify from '@/components/verifition/Verify';

  import userApi from '@/api/system/user';
  import forgotApi from '@/api/forgot';
  import store from '@/store';

  export default {
    data() {
      let validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'));
        } else if (value.length < 8 || value.length > 18) {
          callback(new Error('密码长度需为8-18位'));
        } else if (!/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,18}$/.test(value)) {
          callback(new Error('密码必须是字母和数字的组合，且不能包含符号'));
        } else {
          if (this.temp.confirmPwd !== '') {
            this.$refs.dataForm.validateField('confirmPwd');
          }
          callback();
        }
      };

      let validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.temp.newPwd) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
        newsNum: '', // 未读数量
        operationsFreeSchoolNum: 0,
        currentAdmin: '',
        //屏幕宽度
        screenUWidth: 0,
        count: 0,
        roleTag: '',
        dialogVisible: false,
        arrears: false,
        temp: {
          mobile: '',
          newPwd: '',
          confirmPwd: '',
          smsCode: '',
          source: 'admin'
        },
        rules: {
          mobile: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
              required: true,
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式不正确',
              trigger: 'blur'
            }
          ],
          newPwd: [{ validator: validatePass, trigger: 'blur' }],
          confirmPwd: [{ validator: validatePass2, trigger: 'change' }],
          smsCode: [
            { required: true, message: '请输入验证码', trigger: 'blur' },
            { message: '验证码不得小于6位', trigger: 'blur' }
          ]
        },
        disabledSmsClick: false,
        countdown: '获取验证码',
        token: store.getters.token
      };
    },
    //ErrorLog,
    components: {
      Breadcrumb,
      Hamburger,
      Verify
    },
    computed: {
      ...mapGetters(['sidebar', 'name', 'avatar', 'roles', 'setpayUrl', 'roleName'])
    },
    mounted() {
      // this.getRoleTag();
      const that = this;
      that.searchFormWidth();
      window.onresize = () => {
        if (!that.timer) {
          // 使用节流机制，降低函数被触发的频率
          that.timer = true;
          setTimeout(function () {
            that.$forceUpdate();
            that.searchFormWidth();
            that.timer = false;
          }, 400);
        }
      };

      // authenticationApi.checkAccountBalance().then((res) => {
      //   this.count = res.data.data.money;
      //   this.roleTag = res.data.data.roleTag;
      //   this.operationsFreeSchoolNum = res.data.data.operationsFreeSchoolNum;
      //   localStorage.setItem("roleTag", this.roleTag);
      //   localStorage.setItem(
      //     "operationsFreeSchoolNum",
      //     this.operationsFreeSchoolNum
      //   );
      //   if (this.roleTag == "ExpertsFill") {
      //     if (Number(this.count) > 0) {
      //       this.arrears = false;
      //     } else {
      //       this.arrears = true;
      //     }
      //   }
      // });
    },

    methods: {
      quickBtn() {
        this.dialogVisible = false;
        this.$refs.dataForm.resetFields();
      },
      update() {
        let that = this;
        if (that.roleTag === 'admin') {
          that.$router.push({
            path: '/news/sendNews'
          });
        } else {
          that.$router.push({
            path: '/news/list'
          });
        }
      },
      searchFormWidth() {
        this.screenUWidth = window.innerWidth;
      },

      toggleSideBar() {
        this.$store.dispatch('toggleSideBar');
      },
      logout() {
        this.$store.dispatch('LogOut').then(() => {
          localStorage.removeItem('needRenew');
          location.reload(); // In order to re-instantiate the vue-router object to avoid bugs
        });
      },
      handleUpdatePwd() {
        console.log('🚀🥶💩~ 1', 1);
        this.getPhoneNum();
        this.dialogVisible = true;
        this.dialogVisible = true;
        this.temp.newPwd = '';
        this.temp.confirmPwd = '';
        this.temp.smsCode = '';
        this.countdown = '获取验证码';
        // this.$nextTick(() =>
        //   this.$refs['dataForm'].clearValidate()
        // )
      },

      updatePwd() {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) return;
          const tempData = Object.assign({}, this.temp); //copy obj
          forgotApi.pswReset(tempData).then((res) => {
            this.dialogVisible = false;
            this.$message.success('更新密码成功');
            //清空表单
            this.$refs['dataForm'].resetFields();
          });
        });
      },

      // 获取当前登录用户手机号
      getPhoneNum() {
        userApi.getPhone().then((res) => {
          console.log(res);
          this.temp.mobile = res.data.phone;
        });
      },

      closeSend() {
        const hasS = this.countdown?.indexOf('s') !== -1; // true 或 false
        if (this.countdown === '获取验证码') {
          this.disabledSmsClick = false;
        } else if (this.countdown === '重新获取验证码') {
          this.disabledSmsClick = false;
        } else if (hasS) {
          this.disabledSmsClick = true;
        }
      },
      sendSmg(params) {
        forgotApi.sendSmg(this.temp.mobile, params.captchaVerification).then((res) => {
          console.log(res);
          this.$message.success('短信验证码发送成功，请注意查收');
          var num = 60;
          this.disabledSmsClick = true;
          if (!this.timer) {
            this.timer = setInterval(() => {
              if (num > 0) {
                num--;
                this.countdown = num + 's';
                const hasS = this.countdown?.indexOf('s') !== -1;
                if (hasS) {
                  this.disabledSmsClick = true;
                }
              } else {
                clearInterval(this.timer);
                this.timer = null;
                this.countdown = '重新获取验证码';
                this.disabledSmsClick = false;
              }
            }, 1000);
          }
        });
      },
      //发送验证码
      getSmsClick() {
        const passwordPatrn = /^(?=.*[0-9])(?=.*[a-zA-Z])[0-9a-zA-Z]{8,18}$/; // 仅数字+字母，长度8-18
        if (this.temp.newPwd == '' || this.temp.confirmPwd == '') {
          this.$message.error('请输入密码');
          return;
        }
        if (!passwordPatrn.test(this.temp.newPwd)) {
          this.$message.error('密码必须为8-18位数字和字母的组合，且不能包含符号');
          return false;
        }
        this.$refs.verify.show();
      },

      // 修改密码
      confirm() {
        forgotApi.pswReset(this.temp).then((res) => {
          if (res) {
            this.$message.success('密码修改成功');
            this.dialogVisible = false;
          }
        });
      },

      closeDialog() {
        this.temp.newPwd = '';
        this.temp.confirmPwd = '';
        this.temp.smsCode = '';
        this.countdown = '获取验证码';
      }
    }
  };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  // .navbar .right-menu .avatar-container[data-v-797e31be] {
  //   margin-right: 10px; //影响页面出现横向滚动条 移除
  // }

  .sizeSelect .svg-icon.size-icon {
    margin-bottom: 10px !important;
  }

  .navbar {
    height: 50px;
    line-height: 50px;
    border-radius: 0px !important;
    ::v-deep .el-dialog__body {
      padding: 0 20px !important;
    }
    .jlbLogo {
      position: absolute;
      top: 0;
      left: 50%;
      padding: 0 40px;
      transform: translateX(-100%);
      min-width: 250px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      border-radius: 0 0 5px 5px;
      text-align: center;
      background: linear-gradient(to bottom, #dc9316, #f4ab68);
      margin: 0 auto;
      color: #fff;
    }
    .hamburger-container {
      line-height: 58px;
      height: 50px;
      float: left;
      padding: 0 10px;
    }

    .breadcrumb-container {
      float: left;
    }

    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }

    .right-menu {
      float: right;
      height: 100%;

      &:focus {
        outline: none;
      }

      .right-menu-item {
        display: inline-block;
        margin: 0 8px;
        vertical-align: middle;
      }

      .screenfull {
        height: 20px;
      }

      .international {
        vertical-align: top;
      }

      .theme-switch {
        vertical-align: 15px;
      }

      .avatar-container {
        height: 50px;
        margin-right: 30px;

        .avatar-wrapper {
          cursor: pointer;
          margin-top: 5px;
          position: relative;

          .user-avatar {
            // width: 40px;
            height: 40px;
            border-radius: 10px;
          }

          .el-icon-caret-bottom {
            position: absolute;
            right: -20px;
            top: 25px;
            font-size: 12px;
          }
        }
      }
    }
  }
</style>
