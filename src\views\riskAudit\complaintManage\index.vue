/** * 投诉管理组件 * @component ComplaintManage * @description 投诉管理页面，包含投诉列表展示、搜索、处理等功能 */
<template>
  <div class="app-container">
    <!-- 搜索表单区域 -->
    <div class="frame">
      <el-form ref="searchForm" :model="searchForm" inline label-width="90px">
        <el-row>
          <el-col :span="6" :xs="24">
            <el-form-item label="投诉用户：" prop="user">
              <el-input v-model.trim="searchForm.user" clearable placeholder="请输入名称或ID后四位" size="small" />
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="用户角色：" prop="role">
              <el-select v-model="searchForm.role" clearable placeholder="请选择用户角色" size="small">
                <el-option v-for="(item, index) in selectRole" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="应用：" prop="app">
              <el-select v-model="searchForm.app" clearable filterable placeholder="请选择应用">
                <el-option v-for="item in appOptions" :key="item.appValue" :label="item.appName" :value="item.appValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="padding-left: 20px">
            <el-button icon="el-icon-search" size="mini" type="primary" @click="handleSearch">查询</el-button>
            <el-button icon="el-icon-refresh-left" size="mini" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- Tab切换 -->
    <div class="tab-container">
      <el-tabs v-model="searchForm.processStatus" @tab-click="handleTabClick">
        <el-tab-pane label="全部" name="2" />
        <el-tab-pane label="待处理" name="0" />
        <el-tab-pane label="已处理" name="1" />
      </el-tabs>
    </div>

    <!-- 表格区域 -->
    <div v-loading="loading" class="table-container">
      <div v-show="allData.length < 1" class="nomore">
        <el-image src="https://document.dxznjy.com/automation/1728442200000" style="width: 100px; height: 100px" />
        <div style="color: #999; margin-top: 20px">暂无数据</div>
      </div>
      <el-table v-show="allData.length > 0" :cell-style="{ 'text-align': 'center' }" :data="allData" :header-cell-style="getRowClass" fit size="mini" style="width: 100%">
        <!--        <el-table-column type="selection" width="55" header-align="center" />-->
        <el-table-column header-align="center" label="序号" type="index" width="60" />
        <el-table-column header-align="center" label="投诉Id" prop="id" width="190" />
        <el-table-column header-align="center" label="投诉用户" width="300">
          <template v-slot="{ row }">
            <div class="user-container">
              <el-avatar :size="24" :src="row.complaintUserUrl" />
              <div class="user-info">
                <div style="display: flex">
                  <el-tooltip :content="row.complaintUserName" placement="top">
                    <span class="user-name">{{ row.complaintUserName }}</span>
                  </el-tooltip>
                  <el-tag style="margin-left: 4px">{{ row.roleName }}</el-tag>
                </div>
                <div>{{ row.complaintUserCode }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column header-align="center" label="投诉内容" prop="complaintNumber" width="150">
          <template v-slot="{ row }">
            <el-button type="text" @click="clickBtn(row)">{{ row.complaintNumber || 0 }}条聊天记录</el-button>
          </template>
        </el-table-column>
        <el-table-column header-align="center" label="处理状态" prop="status" width="100">
          <template v-slot="{ row }">
            <el-tag :type="row.processStatus === '待处理' ? 'warning' : 'success'">
              {{ row.processStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="center" label="投诉时间" prop="createTime" width="200">
          <template v-slot="{ row }">
            <span>{{ row.complaintTime || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" label="处理时间" prop="processTime" width="200">
          <template v-slot="{ row }">
            <span>{{ row.processTime || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" label="应用" prop="roleApp" width="100">
          <template v-slot="{ row }">
            <span>{{ row.roleApp || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" label="操作">
          <template v-slot="{ row }">
            <el-button v-if="row.processStatus === '待处理'" size="small" type="text" @click="handleProcess(row)">处理</el-button>
            <el-button v-else size="small" type="text" @click="handleProcess(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页器 -->
    <el-row align="middle" justify="left" style="height: 60px" type="flex">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>
    <DrawContentCenter ref="drawContent" style="height: 100%" @changeStatus="changeStatus" />
  </div>
</template>

<script>
  import DrawContentCenter from './components/DrawContentCenter.vue';
  import informationApi from '@/api/riskAudit/complaintManage';
  import roleApi from '@/api/system/role';

  export default {
    name: 'ComplaintManage',
    components: {
      DrawContentCenter
    },
    data() {
      return {
        /**
         * 搜索表单数据
         * @type {Object}
         * @property {string} user - 投诉用户名称或ID后四位
         * @property {string} role - 用户角色
         * @property {string} app - 应用
         * @property {string} processStatus - 处理状态，默认值'2'表示全部
         * @property {number} pageSize - 每页显示数量
         * @property {number} pageNumber - 当前页码
         */
        searchForm: {
          user: undefined,
          role: undefined,
          app: undefined,
          processStatus: '2',
          pageSize: 10,
          pageNumber: 1
        },
        /**
         * 当前显示的数据
         * @type {Array}
         */
        logData: [],
        /**
         * 过滤后的数据
         * @type {Array}
         */
        filteredData: [],
        /**
         * 加载状态
         * @type {boolean}
         */
        loading: false,
        /**
         * 分页配置
         * @type {Object}
         * @property {number} currentPage - 当前页码
         * @property {number} pageSize - 每页显示数量
         * @property {number} total - 总数据量
         */
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        /**
         * 抽屉组件显示状态
         * @type {boolean}
         */
        drawer: true,
        /**
         * 抽屉组件方向
         * @type {string}
         */
        direction: 'rtl',
        /**
         * 所有数据
         * @type {Array}
         */
        allData: [],
        /**
         * 应用选项列表
         * @type {Array}
         */
        appOptions: [],
        /**
         * 角色选项列表
         * @type {Array}
         */
        selectRole: []
      };
    },

    created() {
      this.loadData();
      this.loadAppData();
      this.fetchRole();
    },
    methods: {
      changeStatus() {
        this.loadData();
      },

      /**
       * 获取角色列表
       * @async
       * @returns {Promise<void>}
       * @description 从API获取所有角色选项
       */
      async fetchRole() {
        try {
          const { data } = await roleApi.getRoles({ friendRoleName: '', role: '', pageNum: 1, pageSize: 100 });
          this.selectRole =
            data.data.map((item) => {
              return {
                label: item.roleName,
                value: item.roleValue
              };
            }) || [];
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
          this.$message.error('获取角色列表失败');
        }
      },

      /**
       * 加载应用数据
       * @async
       * @returns {Promise<void>}
       * @description 从API获取所有应用选项
       */
      async loadAppData() {
        try {
          const res = await roleApi.getAppList();
          this.appOptions = res.data || [];
        } catch (error) {
          console.error('加载应用选项失败:', error);
        }
      },

      /**
       * 点击查看聊天记录
       * @param {Object} row - 当前行数据
       * @description 打开抽屉组件并加载聊天记录数据
       */
      clickBtn(row) {
        this.$refs.drawContent.getMessage(row, row.complaintCode);
        this.$refs.drawContent.drawer = true;
        this.$refs.drawContent.isViewChat = true;
      },

      /**
       * 获取表格行样式
       * @param {Object} { rowIndex } - 行索引
       * @returns {string} 样式字符串
       * @description 为表格第一行添加特殊样式
       */
      getRowClass({ rowIndex }) {
        if (rowIndex === 0) {
          return 'background:#f5f7fa';
        }
      },

      /**
       * 加载数据
       * @async
       * @returns {Promise<void>}
       * @description 根据搜索条件加载投诉列表数据
       */
      async loadData() {
        this.loading = true;
        try {
          const res = await informationApi.informationList(this.searchForm);
          this.allData = res.data.data || [];
          this.pagination.total = res.data.total || 0;
        } catch (error) {
          this.$message.error('数据加载失败');
          console.error(error);
        } finally {
          this.loading = false;
        }
      },

      /**
       * 处理搜索
       * @description 重置页码并重新加载数据
       */
      handleSearch() {
        this.searchForm.currentPage = 1;
        this.loadData();
      },

      /**
       * 处理重置
       * @description 重置搜索表单并重新加载数据
       */
      handleReset() {
        this.$refs.searchForm.resetFields();
        this.pagination.currentPage = 1;
        this.searchForm.pageSize = 10;
        this.searchForm.pageNumber = 1;
        this.pagination.pageSize = 10;
        console.log('🚀🥶💩~ this.searchForm', this.searchForm);
        this.loadData();
      },

      /**
       * 处理每页显示数量变化
       * @param {number} val - 新的每页显示数量
       * @description 更新分页大小并重新加载数据
       */
      handleSizeChange(val) {
        this.searchForm.pageSize = val;
        this.searchForm.pageNumber = 1;
        this.pagination.currentPage = 1;
        this.pagination.pageSize = val;
        this.loadData();
      },

      /**
       * 处理当前页码变化
       * @param {number} val - 新的页码
       * @description 更新当前页码并重新加载数据
       */
      handleCurrentChange(val) {
        this.pagination.currentPage = val;
        this.searchForm.pageNumber = val;
        this.loadData();
      },

      /**
       * 处理标签页切换
       * @description 重置页码并重新加载数据
       */
      handleTabClick() {
        this.pagination.currentPage = 1;
        this.searchForm.user = undefined;
        this.searchForm.role = undefined;
        this.searchForm.app = undefined;
        this.searchForm.pageNumber = 1;

        this.loadData();
      },

      /**
       * 处理投诉
       * @param {Object} row - 当前行数据
       * @description 打开抽屉组件并加载投诉详情
       */
      handleProcess(row) {
        this.$refs.drawContent.getMessage(row, row.complaintCode);
        this.$refs.drawContent.drawer = true;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .frame {
    background-color: rgba(255, 255, 255);
    padding: 0 20px;
  }

  .tab-container {
    background-color: #fff;
    padding: 0 20px; /* 与.frame和.table-container的padding对齐 */
  }

  .table-container {
    padding: 0 20px 20px;
    background-color: #fff;
    border-radius: 8px;
  }

  .nomore {
    width: 100%;
    height: 100%;
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .user-container {
    display: flex;
    align-items: center;
    justify-content: left;
  }

  .user-info {
    margin-left: 8px;
    text-align: left;
    font-size: 12px;
    color: #606266;
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;

    .user-name {
      text-align: left;
      font-size: 12px;
      color: #606266;
      display: inline-block;
      max-width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }
  }
</style>
