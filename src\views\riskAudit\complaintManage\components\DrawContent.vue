<template>
  <el-drawer :visible.sync="drawer" :direction="direction" size="40%" :before-close="handleClose" :with-header="false">
    <div class="drawer-container">
      <div class="header">
        <h2>{{ isCreating ? '新建规则 - 敏感词' : isEditing ? '编辑规则 - 敏感词' : '规则详情' }}</h2>
        <i class="el-icon-close" @click="handleClose" />
      </div>
      <el-form ref="ruleForm" :label-position="labelPosition" :model="localRule" :rules="rules" label-width="120px" class="rule-form">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="localRule.ruleName" :disabled="!isEditing && !isCreating" :maxlength="50" show-word-limit placeholder="请输入规则名称" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="监控对象" prop="scope">
              <el-select v-model="localRule.scope" :disabled="!isEditing && !isCreating" placeholder="请选择会话类型" style="width: 200px">
                <el-option v-for="channel in monitorObject" :key="channel.value" :label="channel.name" :value="channel.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" " prop="ruleObject">
              <el-select
                collapse-tags
                v-model="localRule.ruleObject"
                multiple
                :disabled="!isEditing && !isCreating"
                placeholder="请选择渠道角色"
                style="margin-left: 10px; width: 200px"
              >
                <el-option v-for="channel in roleList" :key="channel.value" :label="channel.label" :value="channel.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="特殊条件">
          <el-checkbox label="识别手机号" :true-label="1" :false-label="0" v-model="localRule.mobileIsRecognized" :disabled="!isEditing && !isCreating" />
        </el-form-item>
        <el-form-item label="监控策略">
          <el-table :data="localRule.riskStrategyCos" style="width: 100%">
            <el-table-column label="序号" type="index" />

            <el-table-column label="敏感词表">
              <template v-slot="scope">
                <el-form-item :prop="`riskStrategyCos[${scope.$index}].sensitiveWordCode`" :rules="[{ required: true, message: '请选择敏感词表', trigger: 'change' }]">
                  <el-select v-model="scope.row.sensitiveWordCode" clearable :disabled="!isEditing && !isCreating" placeholder="请选择敏感词表" style="width: 100%">
                    <el-option v-for="item in sensitiveList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column label="过滤词表">
              <template v-slot="scope">
                <el-form-item :prop="`riskStrategyCos[${scope.$index}].filterWordCode`" :rules="[{ required: false, message: '请选择过滤词表', trigger: 'change' }]">
                  <el-select v-model="scope.row.filterWordCode" clearable :disabled="!isEditing && !isCreating" placeholder="请选择过滤词表" style="width: 100%">
                    <el-option v-for="item in filterList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column label="通知方式">
              <template v-slot="scope">
                {{ scope.row.notifyMethod }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="100" v-if="isEditing || isCreating">
              <template v-slot="scope">
                <el-button type="text" style="color: #ff4d4f" :disabled="localRule.riskStrategyCos.length <= 1" @click="removeStrategy(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-button v-if="(isEditing || isCreating) && localRule.riskStrategyCos.length < 5" type="text" @click="addStrategy" style="margin-top: 10px">+ 添加监控策略</el-button>
        </el-form-item>

        <el-form-item v-if="isEditing || isCreating">
          <el-button type="primary" :loading="isSubmitLoading" @click="submitForm">保存</el-button>
          <el-button @click="handleClose">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>

<script>
  export default {
    name: 'DrawContent',
    props: {
      currentRule: { type: Object, default: () => ({}) },
      isEditing: Boolean,
      isCreating: Boolean,
      isWatchSubmitLoading: Boolean,
      roleList: { type: Array, default: () => [] },
      sensitiveList: { type: Array, default: () => [] },
      filterList: { type: Array, default: () => [] },
      monitorObject: { type: Array, default: () => [] }
    },
    data() {
      return {
        labelPosition: 'top',
        drawer: false, // 默认关闭，由父组件控制
        direction: 'rtl',
        localRule: {
          ruleName: '',
          ruleCode: undefined,
          scope: '',
          id: '',
          ruleObject: [],
          mobileIsRecognized: 0,
          riskStrategyCos: [{ notifyMethod: '仅做记录' }]
        },
        rules: {
          ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
          scope: [{ required: false, message: '请选择会话类型', trigger: 'change' }],
          ruleObject: [{ required: true, message: '请选择渠道角色', trigger: 'change' }],
          riskStrategyCos: [
            {
              type: 'array',
              required: true,
              validator: (_rule, value, callback) => {
                if (!value.length) {
                  return callback(new Error('请至少选择一个敏感词表'));
                }

                const invalidItems = value.filter((item) => {
                  // return !item.sensitiveWordCode || !item.filterWordCode;
                  return !item.sensitiveWordCode;
                });

                if (invalidItems.length) {
                  const errorMsg = invalidItems
                    .map((item, index) => {
                      let msg = `第 ${index + 1} 条`;
                      // if (!item.sensitiveWordCode ) {
                      //   msg += ' 未选择敏感词和过滤词';
                      // } else
                      if (!item.sensitiveWordCode) {
                        msg += ' 未选择敏感词';
                      } else {
                        msg += ' 未选择过滤词';
                      }
                      return msg;
                    })
                    .join('、');

                  return callback(new Error(errorMsg));
                }

                callback();
              },
              trigger: 'change'
            }
          ]
        },
        isSubmitLoading: false
      };
    },

    watch: {
      currentRule: {
        immediate: true,
        handler(newVal) {
          // 1. 首先检查newVal是否为null/undefined
          if (!newVal) {
            this.localRule = this.getDefaultRule(); // 使用默认值
            return;
          }
          // 2. 确保riskStrategyCos存在且是数组
          const riskStrategyCos = Array.isArray(newVal.riskStrategyCos) ? newVal.riskStrategyCos : [];

          // 3. 安全构建localRule对象
          this.localRule = {
            ruleName: newVal.ruleName || '',
            id: newVal.id || '',
            scope: newVal.scope || '', // 添加默认值
            mobileIsRecognized: newVal.mobileIsRecognized || 0,
            ruleObject: Array.isArray(newVal.ruleObject) ? newVal.ruleObject : [],
            riskStrategyCos: riskStrategyCos.map((strategy) => ({
              sensitiveWordCode: strategy?.sensitiveWordCode || '',
              filterWordCode: strategy?.filterWordCode || '',
              notifyMethod: strategy?.notifyMethod || '仅做记录'
            }))
          };
        }
      },
      drawer(newVal) {
        if (!newVal) {
          this.resetForm();
        }
      }
    },

    methods: {
      // 提取默认值逻辑
      getDefaultRule() {
        return {
          ruleName: '',
          scope: '',
          mobileIsRecognized: 0,
          ruleObject: [],
          riskStrategyCos: []
        };
      },
      handleClose() {
        this.$emit('close');
        this.drawer = false;
      },
      addStrategy() {
        if (this.localRule.riskStrategyCos.length < 5) {
          this.localRule.riskStrategyCos.push({ notifyMethod: '仅做记录' });
        }
      },
      removeStrategy(index) {
        if (this.localRule.riskStrategyCos.length > 1) {
          this.localRule.riskStrategyCos.splice(index, 1);
        }
      },
      submitForm() {
        this.$refs.ruleForm.validate((valid) => {
          // 创建 value => label 的映射表
          const filterLabelMap = Object.fromEntries(this.filterList.map((item) => [item.value, item.label]));
          const sensitiveLabelMap = Object.fromEntries(this.sensitiveList.map((item) => [item.value, item.label]));

          const result = this.localRule.riskStrategyCos.map((item) => ({
            ...item,
            // filterWordName: filterLabelMap[item.filterWordCode] || '未知分类',
            sensitiveWordName: sensitiveLabelMap[item.sensitiveWordCode] || '未知分类'
          }));
          // console.log('🚀🥶💩~ result', result);

          if (valid) {
            this.isSubmitLoading = true;
            const ruleToSave = {
              ...this.localRule,
              ruleObjectValue: this.roleList
                .filter((item) => this.localRule.ruleObject.includes(item.value))
                .map((item) => item.label)
                .join('、'),
              ruleObject: this.localRule.ruleObject.join('、'),
              riskStrategyCos: result // 将完整策略提交给后端
            };
            // console.log('🚀🥶💩~ ruleToSave', ruleToSave);
            this.$emit('saveRule', ruleToSave);
          } else {
            console.log('🚀🥶💩~ 没有选择全哦' + '');
          }
        });
      },
      resetForm() {
        this.localRule = {
          ruleName: '',
          scope: '',
          ruleObject: [],
          mobileIsRecognized: 0,
          riskStrategyCos: []
        };
        this.$refs.ruleForm.resetFields(); // 重置表单验证状态
      }
    }
  };
</script>

<style lang="scss" scoped>
  .drawer-container {
    padding: 20px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header h2 {
    font-size: 18px;
    color: #333;
  }

  .el-icon-close {
    font-size: 24px;
    cursor: pointer;
  }

  .rule-form {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
  }

  .strategy-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
</style>
