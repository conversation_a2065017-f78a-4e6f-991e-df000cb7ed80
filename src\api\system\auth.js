/**
 * 登录相关接口
 */
import request from '@/utils/request';

const prefix = 'im';

export default {
  verificationCode(gloadId) {
    return request({
      url: '/new/security/login/verification/code/' + gloadId,
      method: 'get'
    });
  },

  loginByUsername(username, password, userType, code, appId, role, roleSource) {
    return request({
      // url: '/new/security/v2/login/trainAdminLogin',
      url: '/new/security/im/login',
      method: 'get',
      params: { password, role, username, userType, code, appId, roleSource }
    });
  },
  byLoginNameQueryRoleTag(data) {
    return request({
      url: prefix + '/api/sys-user/select-roleTag',
      method: 'GET',
      params: data
    });
  },
  getUserInfo(token) {
    return request({
      url: prefix + '/api/sys-user/info',
      method: 'GET'
    });
  },
  logout() {
    return request({
      url: prefix + '/api/sys-user/logout',
      method: 'PUT'
    });
  }
};
