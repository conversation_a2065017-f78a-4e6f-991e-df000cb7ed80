/** * 用户管理组件 * @component UserManagement * @description 用户管理页面，包含组织架构树和用户列表，支持用户的增删改查、角色管理等功能 */
<template>
  <div class="app-container">
    <div class="content-container">
      <!-- 左侧组织架构树 -->
      <div class="organizational-structure">
        <h3>组织架构</h3>
        <el-input v-model="groupName" @input="fetchGroup" placeholder="请输入组织名称" style="margin-bottom: 20px"></el-input>

        <div class="tree-container">
          <el-tree
            ref="treeRef"
            :default-expanded-keys="expandedKeys"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            draggable
            :allow-drop="allowDrop"
            @node-click="handleNodeClick"
            @node-expand="handleExpand"
            @node-collapse="handleCollapse"
          >
            <template #default="{ data, node }">
              <div class="custom-tree-node">
                <span>{{ data.name }}</span>
                <el-dropdown trigger="click" @command="handleCommand($event, data)" @click.native.stop>
                  <span class="el-dropdown-link">
                    <i class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit" v-if="node.level > 1">编辑</el-dropdown-item>
                    <el-dropdown-item command="addChild" v-if="node.level <= 4">新建子部门</el-dropdown-item>
                    <el-dropdown-item command="delete" v-if="node.level > 1">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧搜索和列表 -->
      <div class="user-list-room">
        <el-row class="search-row">
          <el-input v-model.trim="tableQuery.mobile" style="width: 200px" placeholder="手机号" />
          <span style="margin-right: 15px" />
          <el-input v-model.trim="tableQuery.realName" style="width: 200px" placeholder="用户姓名" />
          <span style="margin-right: 15px" />
          <el-select v-model="tableQuery.roleId" placeholder="请选择用户角色" clearable filterable>
            <el-option v-for="item in selectRole" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <span style="margin-right: 15px" />

          <span style="margin-right: 15px" />
          <el-button type="primary" icon="el-icon-search" size="small" @click="fetchData()">搜索</el-button>
          <el-button type="default" icon="el-icon-refresh" size="small" @click="handleReset()">重置</el-button>
        </el-row>
        <div style="margin-bottom: 30px" />
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">{{ textMap.create }}</el-button>
        <div style="margin-bottom: 30px" />

        <el-table v-loading="tableLoading" :data="tableData" size="small" style="width: 100%" fit highlight-current-row :header-cell-style="getRowClass">
          <el-table-column prop="id" label="用户Id" width="200" />
          <el-table-column prop="realName" label="用户姓名" width="250" />
          <el-table-column prop="mobile" label="手机号" width="150" />
          <el-table-column prop="role" label="角色">
            <template slot-scope="scope">
              <el-tag style="margin-right: 4px" v-for="item in scope.row.roles" :key="item.value" :type="item.name === '超级大管理员' ? 'success' : 'primary'" size="mini" plain>
                {{ item.name }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" width="200" label="创建时间" />
          <el-table-column label="操作">
            <template v-slot="scope">
              <el-tooltip content="编辑" placement="top">
                <el-button @click="handleUpdate(scope.row)" size="medium" type="info" icon="el-icon-edit" circle plain></el-button>
              </el-tooltip>
              <el-tooltip content="修改权限" placement="top">
                <el-button @click="handleUpdateRolePerms(scope.row)" size="medium" type="warning" icon="el-icon-view" circle plain></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button @click="handleDelete(scope.$index, scope.row)" size="medium" type="danger" icon="el-icon-delete" circle plain></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            :current-page="tablePage.current"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="tablePage.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="30%">
          <el-form ref="userForm" :rules="rules" :model="temp" label-position="right" label-width="80px">
            <el-form-item label="用户姓名" prop="realName">
              <el-input v-model="temp.realName" :disabled="!!temp.id" placeholder="请输入用户姓名" show-word-limit maxlength="10" />
            </el-form-item>
            <el-form-item label="手机号" prop="userName">
              <el-input v-model="temp.userName" :disabled="!!temp.id" placeholder="请输入手机号" />
            </el-form-item>
            <el-form-item label="角色" prop="role" v-if="dialogStatus === 'create'">
              <el-select v-model="temp.role" placeholder="请选择用户角色" clearable filterable multiple>
                <el-option v-for="item in selectRole" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input v-model="temp.password" type="password" show-password placeholder="请输入密码（可选）" />
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input v-model="temp.confirmPassword" type="password" show-password placeholder="请再次输入密码（可选）" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()" :loading="dialogStatus === 'create' ? createUserLoading : updateUserLoading">
              确定
            </el-button>
          </div>
        </el-dialog>

        <el-dialog title="修改用户权限" :visible.sync="editRolesDialogVisible" width="60%" :close-on-click-modal="false">
          <div>
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
            <div style="margin: 15px 0"></div>
            <el-checkbox-group v-model="updateUserRolesData.rids">
              <el-checkbox class="role-checkbox" v-for="role in selectRole" :label="role.value" :key="role.value">
                {{ role.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="editRolesDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="checkUpdateUserRolesData" :loading="updateRoleLoading">确定</el-button>
          </div>
        </el-dialog>

        <!-- 修改编辑部门的对话框 -->
        <el-dialog title="编辑部门" :visible.sync="editDeptDialogVisible" width="30%">
          <el-form ref="editDeptForm" :model="editForm" :rules="editRules" label-width="80px">
            <el-form-item label="部门名称" prop="name">
              <el-input v-model="editForm.name" placeholder="请输入部门名称" />
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="editForm.sort" :min="1" :max="10000" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="editDeptDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitEditDept" :loading="editDeptLoading">确定</el-button>
          </div>
        </el-dialog>

        <!-- 修改新增部门的对话框 -->
        <el-dialog title="新建子部门" :visible.sync="addDeptDialogVisible" width="30%">
          <el-form ref="addDeptForm" :model="addForm" :rules="editRules" label-width="80px">
            <el-form-item label="部门名称" prop="name">
              <el-input v-model="addForm.name" placeholder="请输入部门名称" />
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="addForm.sort" :min="1" :max="10000" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="addDeptDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitAddDept" :loading="addDeptLoading">确定</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
  import debounce from 'lodash/debounce';
  import roleApi from '@/api/system/role';
  import userApi from '@/api/system/user';

  export default {
    name: 'UserManagement',
    data() {
      /**
       * 密码验证规则
       * @param {Object} _rule - 验证规则对象
       * @param {string} value - 密码值
       * @param {Function} callback - 验证回调函数
       */
      const validatePassword = (_rule, value, callback) => {
        if (value) {
          // 密码必须包含数字和字母，长度8-18位
          const passwordRegex = /^(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9]{8,18}$/;
          if (!passwordRegex.test(value)) {
            callback(new Error('密码必须包含数字和字母，长度在8-18位之间'));
          } else if (this.temp.confirmPassword && this.temp.confirmPassword !== value) {
            callback(new Error('两次输入密码不一致'));
          } else {
            callback();
          }
        } else {
          // 未填写密码时，如果确认密码已填写，则触发确认密码校验
          if (this.temp.confirmPassword) {
            this.$refs.userForm.validateField('confirmPassword');
          }
          callback();
        }
      };

      /**
       * 确认密码验证规则
       * @param {Object} _rule - 验证规则对象
       * @param {string} value - 确认密码值
       * @param {Function} callback - 验证回调函数
       */
      const validateConfirmPassword = (_rule, value, callback) => {
        if (this.temp.password) {
          // 如果密码已填写
          if (!value) {
            callback(new Error('请填写确认密码'));
          } else if (value !== this.temp.password) {
            callback(new Error('两次输入密码不一致'));
          } else {
            callback();
          }
        } else {
          // 如果密码未填写，则确认密码必须为空
          if (value) {
            callback(new Error('请先填写密码'));
          } else {
            callback();
          }
        }
      };

      return {
        tableLoading: false,
        tableData: [],
        tableQuery: {
          mobile: '',
          realName: '',
          roleId: '',
          pageNum: 1,
          pageSize: 10
        },
        tablePage: {
          current: 1,
          pages: 0,
          size: 10,
          total: 0
        },
        treeData: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        groupName: '',
        textMap: {
          update: '编辑用户',
          create: '新增用户'
        },
        rules: {
          realName: [
            { required: true, message: '请输入用户名', trigger: 'blur' },
            { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
          ],
          userName: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
          ],
          role: [{ required: true, message: '请选择角色', trigger: 'change' }],
          password: [{ validator: validatePassword, trigger: 'blur' }],
          confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }]
        },
        temp: {
          id: '',
          realName: '',
          userName: '',
          role: '',
          password: '',
          confirmPassword: ''
        },
        dialogStatus: '',
        dialogFormVisible: false,
        editRolesDialogVisible: false,

        checkAll: false,
        isIndeterminate: true,
        //所有角色(管理员除外)
        selectRole: [],
        roleMap: new Map(),
        // 更新用户的角色的数据
        updateUserRolesData: {
          idx: null,
          id: null,
          rids: []
        },
        editDeptDialogVisible: false,
        currentDept: null,
        editForm: {
          name: '',
          sort: 1
        },
        editRules: {
          name: [
            { required: true, message: '请输入部门名称', trigger: 'blur' },
            { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
          ],
          sort: [
            { required: true, message: '请输入排序', trigger: 'change' },
            { type: 'number', min: 1, max: 10000, message: '排序必须在 1 到 10000 之间', trigger: 'change' }
          ]
        },
        addDeptDialogVisible: false,
        currentParentDept: null,
        addForm: {
          name: '',
          sort: 1
        },
        defaultDeptId: '',
        // 添加 loading 状态变量
        editDeptLoading: false,
        addDeptLoading: false,
        updateUserLoading: false,
        createUserLoading: false,
        updateRoleLoading: false,
        processDrawerVisible: false,
        processData: {},
        processLoading: false,
        originalTreeData: [], // 添加原始树数据存储
        expandedKeys: [] // 添加原始树数据存储
      };
    },
    created() {
      this.fetchTree();
      this.fetchRole();
    },
    watch: {
      'tableQuery.mobile': debounce(function () {
        this.tableQuery.pageNum = 1;
        this.tablePage.current = 1;
        this.fetchData();
      }, 500),
      'tableQuery.userName': debounce(function () {
        this.tableQuery.pageNum = 1;
        this.tablePage.current = 1;
        this.fetchData();
      }, 500),
      'tableQuery.role': debounce(function () {
        this.tableQuery.pageNum = 1;
        this.tablePage.current = 1;
        this.fetchData();
      }, 500)
    },
    methods: {
      // 只能同级拖拽
      allowDrop(draggingNode, dropNode, type) {
        const draggingParent = draggingNode.parent;
        const dropParent = dropNode.parent;
        // 同一个父节点才允许 before/after 拖拽，不允许 inner 嵌套
        return draggingParent === dropParent && type !== 'inner';
      },

      /**
       * 获取组织架构树数据
       * @async
       * @returns {Promise<void>}
       * @description 获取组织架构树数据，并设置默认选中的部门ID
       */
      async fetchTree() {
        try {
          this.expandedKeys = this.$refs.deptTree?.getExpandedKeys?.() || [];
          // let arr = this.$refs.deptTree?.getExpandedKeys(); // 保存展开节点的 id
          // console.log('🚀🥶💩~ arr', arr);
          console.log('🚀🥶💩~ this.expandedKeys', this.expandedKeys);
          const { data, code } = await userApi.getOrgTree();
          if (code === 20000) {
            this.originalTreeData = data; // 保存原始数据
            this.treeData = data;
            this.defaultDeptId = data[0].id;
            await this.fetchData();
          }
        } catch (error) {
          this.treeData = [];
          this.originalTreeData = [];
          console.error(error, 'error');
        }
      },

      handleExpand(data, node) {
        if (!this.expandedKeys.includes(data.id)) {
          this.expandedKeys.push(data.id);
        }
      },
      handleCollapse(data, node) {
        const index = this.expandedKeys.indexOf(data.id);
        if (index > -1) {
          this.expandedKeys.splice(index, 1);
        }
      },

      /**
       * 根据组织名称筛选组织架构树
       * @description 根据输入的组织名称筛选组织架构树，支持模糊匹配，并自动展开匹配的节点
       */
      fetchGroup() {
        if (!this.groupName) {
          // 如果搜索框为空，恢复原始树数据
          this.treeData = JSON.parse(JSON.stringify(this.originalTreeData));
          return;
        }

        // 递归搜索匹配的节点
        const searchNodes = (nodes, keyword) => {
          return nodes.filter((node) => {
            // 检查当前节点是否匹配
            const isMatch = node.name.toLowerCase().includes(keyword.toLowerCase());

            // 如果有子节点，递归搜索子节点
            if (node.children && node.children.length > 0) {
              const matchedChildren = searchNodes(node.children, keyword);
              // 如果子节点有匹配，保留当前节点
              if (matchedChildren.length > 0) {
                node.children = matchedChildren;
                return true;
              }
            }

            // 返回当前节点是否匹配
            return isMatch;
          });
        };

        // 对原始树数据进行深拷贝，避免修改原始数据
        const originalTree = JSON.parse(JSON.stringify(this.originalTreeData));

        // 执行搜索
        const filteredTree = searchNodes(originalTree, this.groupName);

        // 更新树数据
        this.treeData = filteredTree;
        // 在下一个 tick 中展开所有节点
        this.$nextTick(() => {
          const tree = this.$refs.treeRef;
          if (tree) {
            // 获取所有节点
            const nodes = tree.store.nodesMap;
            // 遍历所有节点并展开
            Object.keys(nodes).forEach((key) => {
              const node = nodes[key];
              if (node) {
                node.expanded = true;
              }
            });
            // 如果有匹配的节点，选中第一个节点
            if (filteredTree.length > 0) {
              tree.setCurrentKey(filteredTree[0].id);
            }
          }
        });
      },

      /**
       * 处理更新用户角色权限
       * @param {Object} row - 用户行数据
       * @description 打开修改用户角色对话框，并初始化角色数据
       */
      handleUpdateRolePerms(row) {
        this.updateUserRolesData.idx = 0;
        this.updateUserRolesData.id = row.id;
        this.updateUserRolesData.rids = row.roles.map((item) => {
          return item.val;
        });
        this.editRolesDialogVisible = true;
      },

      /**
       * 检查并确认更新用户角色数据
       * @description 检查是否选中了角色，如果没有选中则提示用户确认
       */
      checkUpdateUserRolesData() {
        const noRolesSelected = this.updateUserRolesData && this.updateUserRolesData.rids && this.updateUserRolesData.rids.length === 0;
        if (noRolesSelected) {
          this.$confirm('当前没有选中任何角色，会清除该用户已有的角色, 是否继续?', '提示', confirm)
            .then(() => {
              this.invokeUpdateUserRolesApi();
            })
            .catch(() => {
              this.$message('已取消编辑用户角色');
            });
        } else {
          this.invokeUpdateUserRolesApi();
        }
      },

      /**
       * 调用更新用户角色API
       * @async
       * @returns {Promise<void>}
       * @description 调用API更新用户角色，并刷新用户列表
       */
      async invokeUpdateUserRolesApi() {
        this.updateRoleLoading = true;
        try {
          let that = this;
          await userApi.updateUserRole(that.updateUserRolesData);
          this.$message.success('更新用户角色成功');
          this.editRolesDialogVisible = false;
          await this.fetchData();
        } catch (error) {
          this.$message.error('更新用户角色失败');
        } finally {
          this.updateRoleLoading = false;
        }
      },

      /**
       * 处理全选/取消全选角色
       * @param {boolean} val - 是否全选
       * @description 根据全选状态更新选中的角色列表
       */
      handleCheckAllChange(val) {
        let allRids = this.selectRole.map((role) => role.value);
        this.updateUserRolesData.rids = val ? allRids : [];
        this.isIndeterminate = false;
      },

      /**
       * 获取表格行样式
       * @param {Object} { rowIndex } - 行索引
       * @returns {string} 样式字符串
       * @description 为表格第一行添加特殊样式
       */
      getRowClass({ rowIndex }) {
        return rowIndex === 0 ? 'background:#f5f7fa' : '';
      },

      /**
       * 获取角色列表
       * @async
       * @returns {Promise<void>}
       * @description 获取所有角色列表（管理员除外）
       */
      async fetchRole() {
        try {
          const res = await roleApi.listRole();
          this.selectRole = res.data || [];
        } catch (error) {
          this.$message.error('获取角色列表失败');
        }
      },

      /**
       * 获取用户列表数据
       * @async
       * @returns {Promise<void>}
       * @description 根据查询条件获取用户列表数据
       */
      async fetchData() {
        this.tableLoading = true;
        try {
          // 确保分页参数正确
          const params = {
            ...this.tableQuery,
            pageNum: this.tablePage.pages,
            pageSize: this.tablePage.size,
            deptId: this.defaultDeptId
          };

          const { data } = await userApi.getUser(params);

          // 更新表格数据
          this.tableData = data.data || [];
          this.tablePage.total = parseInt(data.totalItems);
        } catch (error) {
          this.$message.error('数据加载失败');
          console.error(error);
        } finally {
          this.tableLoading = false;
        }
      },

      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },

      handleCurrentChange(val) {
        this.tablePage.pages = val;
        this.fetchData();
      },

      /**
       * 重置查询条件
       * @description 重置所有查询条件到默认值
       */
      handleReset() {
        this.tableQuery = {
          mobile: '',
          realName: '',
          roleId: '',
          pageNum: 1,
          pageSize: 10
        };
        this.tablePage.pages = 1;
        this.fetchData();
      },

      /**
       * 处理更新用户
       * @param {Object} row - 用户行数据
       * @description 打开编辑用户对话框，并初始化用户数据
       */
      handleUpdate(row) {
        this.temp = {
          id: row.id,
          realName: row.realName,
          userName: row.mobile,
          sysDeptId: row.sysDeptId,
          role:
            row.roles.map((item) => {
              return item.val;
            }) || []
        };

        this.dialogStatus = 'update';
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs.userForm.clearValidate();
        });
      },

      /**
       * 处理创建新用户
       * @description 打开创建用户对话框，并初始化表单数据
       */
      handleCreate() {
        this.temp = {
          id: '',
          realName: '',
          userName: '',
          role: '',
          password: '',
          confirmPassword: ''
        };
        this.dialogStatus = 'create';
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs.userForm.clearValidate();
        });
      },

      /**
       * 创建新用户数据
       * @description 验证表单并提交新用户数据
       */
      createData() {
        this.$refs.userForm.validate(async (valid) => {
          if (!valid) return;
          this.createUserLoading = true;
          try {
            const newUser = {
              id: '',
              sysDeptId: this.defaultDeptId, // 使用当前选中的部门ID
              realName: this.temp.realName,
              userName: this.temp.userName,
              roleIds: this.temp.role,
              password: this.temp.password || undefined
            };
            await userApi.addUser(newUser);
            await this.fetchData();
            this.dialogFormVisible = false;
            this.$message.success('用户添加成功');
          } catch (error) {
            this.$message.error('创建用户失败');
            console.error(error);
          } finally {
            this.createUserLoading = false;
          }
        });
      },

      /**
       * 更新用户数据
       * @description 验证表单并提交用户更新数据
       */
      updateData() {
        this.$refs.userForm.validate(async (valid) => {
          if (!valid) return;
          this.updateUserLoading = true;
          try {
            const updateUser = {
              sysDeptId: this.defaultDeptId,
              userName: this.temp.userName,
              password: this.temp.password
            };
            await userApi.updateUser(updateUser);
            await this.fetchData();
            this.dialogFormVisible = false;
            this.$message.success('用户更新成功');
          } catch (error) {
            this.$message.error('更新用户失败');
            console.error(error);
          } finally {
            this.updateUserLoading = false;
          }
        });
      },

      /**
       * 处理树节点点击事件
       * @param {Object} data - 节点数据
       * @description 更新选中的部门ID并刷新用户列表
       */
      handleNodeClick(data) {
        this.defaultDeptId = data.id;
        this.fetchData();
      },

      /**
       * 处理树节点命令
       * @param {string} command - 命令类型
       * @param {Object} data - 节点数据
       * @description 根据命令类型执行不同的部门操作
       */
      handleCommand(command, data) {
        switch (command) {
          case 'edit':
            this.handleEdit(data);
            break;
          case 'addChild':
            this.handleAddChild(data);
            break;
          case 'delete':
            this.handleDeleteDept(data);
            break;
        }
      },

      /**
       * 处理编辑部门
       * @param {Object} data - 部门数据
       * @description 打开编辑部门对话框，并初始化部门数据
       */
      handleEdit(data) {
        this.currentDept = data;
        this.editForm = {
          name: data.name,
          sort: data.sort || 1
        };
        this.editDeptDialogVisible = true;
        this.$nextTick(() => {
          this.$refs.editDeptForm.clearValidate();
        });
      },

      /**
       * 提交编辑部门
       * @description 验证表单并提交部门编辑数据
       */
      submitEditDept() {
        this.$refs.editDeptForm.validate(async (valid) => {
          if (!valid) return;
          this.editDeptLoading = true;
          try {
            const params = {
              id: this.currentDept.id,
              name: this.editForm.name,
              parentId: this.currentDept.parentId,
              sort: this.editForm.sort,
              path: this.currentDept.path || '',
              level: this.currentDept.level || '',
              code: this.currentDept.code || ''
            };
            const res = await userApi.editDept(params);
            if (res.code === 20000) {
              this.editDeptDialogVisible = false;
              this.$message.success('操作成功');
              await this.fetchTree();
            } else {
              this.$message.error(res.message || '修改部门名称失败');
            }
          } catch (error) {
            this.$message.error('修改部门名称失败');
            console.error(error);
          } finally {
            this.editDeptLoading = false;
          }
        });
      },

      /**
       * 处理添加子部门
       * @param {Object} data - 父部门数据
       * @description 打开添加子部门对话框，并初始化表单数据
       */
      handleAddChild(data) {
        this.currentParentDept = data;
        let maxSort = 1;
        if (data.children && data.children.length > 0) {
          maxSort = Math.max(...data.children.map((item) => item.sort)) + 1;
        }
        this.addForm = {
          name: '',
          sort: maxSort // 使用固定的初始值，确保满足验证规则
        };
        this.addDeptDialogVisible = true;
        this.$nextTick(() => {
          this.$refs.addDeptForm.clearValidate();
        });
      },

      /**
       * 提交新增部门
       * @description 验证表单并提交新增部门数据
       */
      submitAddDept() {
        this.$refs.addDeptForm.validate(async (valid) => {
          if (!valid) return;
          this.addDeptLoading = true;
          try {
            const params = {
              name: this.addForm.name,
              parentId: this.currentParentDept.id,
              sort: this.addForm.sort
            };
            const res = await userApi.addChildDept(params);
            if (res.code === 20000) {
              this.addDeptDialogVisible = false;
              this.$message.success(`成功添加子部门: ${this.addForm.name}`);
              await this.fetchTree();
            } else {
              this.$message.error(res.message || '添加子部门失败');
            }
          } catch (error) {
            this.$message.error('添加子部门失败');
            console.error(error);
          } finally {
            this.addDeptLoading = false;
          }
        });
      },

      /**
       * 检查用户是否具有管理员角色
       * @param {Object} row - 用户行数据
       * @returns {boolean} 是否具有管理员角色
       * @description 判断用户是否具有超级管理员角色
       */
      hasAdminRole(row) {
        return row.roles?.some((role) => role.name === '超级大管理员') || false;
      },

      /**
       * 处理删除用户
       * @param {number} index - 用户索引
       * @param {Object} row - 用户行数据
       * @description 删除指定用户，并更新用户列表
       */
      handleDelete(index, row) {
        this.$confirm(`确定删除用户 "${row.realName}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            this.tableLoading = true;
            try {
              await userApi.deleteUser(row.id);
              this.tableData.splice(index, 1);
              this.tablePage.total--;
              await this.fetchData();
              this.$message.success('删除成功');
            } catch (error) {
              this.$message.error('删除失败');
              console.error(error);
            } finally {
              this.tableLoading = false;
            }
          })
          .catch(() => {
            this.$message.info('取消删除');
          });
      },

      /**
       * 处理删除部门
       * @param {Object} data - 部门数据
       * @description 删除指定部门，并更新组织架构树
       */
      handleDeleteDept(data) {
        this.$confirm(`确定删除部门 "${data.name}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              const res = await userApi.deleteDept(data.id);
              if (res.code === 20000) {
                this.$message.success('删除部门成功');
                await this.fetchTree();
              } else {
                this.$message.error(res.message || '删除部门失败');
              }
            } catch (error) {
              this.$message.error('删除部门失败');
              console.error(error);
            }
          })
          .catch(() => {
            this.$message.info('取消删除');
          });
      },

      /**
       * 处理按钮点击事件
       * @param {Object} row - 用户行数据
       * @description 打开处理抽屉，并初始化处理数据
       */
      handleProcess(row) {
        this.processData = { ...row };
        this.processDrawerVisible = true;
      },

      /**
       * 处理抽屉关闭事件
       * @param {Function} done - 关闭回调函数
       * @description 确认是否关闭处理抽屉
       */
      handleDrawerClose(done) {
        this.$confirm('确认关闭？')
          .then((_) => {
            done();
          })
          .catch((_) => {});
      },

      /**
       * 完成处理
       * @async
       * @description 调用API完成处理，并更新用户列表
       */
      async handleCompleteProcess() {
        this.processLoading = true;
        try {
          // 调用完成处理的API
          await userApi.completeProcess(this.processData.id);
          this.$message.success('处理完成');
          this.processDrawerVisible = false;
          await this.fetchData();
        } catch (error) {
          this.$message.error('处理失败');
          console.error(error);
        } finally {
          this.processLoading = false;
        }
      },

      /**
       * 取消处理
       * @async
       * @description 调用API取消处理，并更新用户列表
       */
      async handleCancelProcess() {
        this.processLoading = true;
        try {
          // 调用取消处理的API
          await userApi.cancelProcess(this.processData.id);
          this.$message.success('已取消处理');
          this.processDrawerVisible = false;
          await this.fetchData();
        } catch (error) {
          this.$message.error('取消处理失败');
          console.error(error);
        } finally {
          this.processLoading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  :global(html),
  :global(body) {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden;
  }

  .app-container {
    height: calc(100vh - 60px);
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .content-container {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .organizational-structure {
    width: 15%;
    border: 1px solid #bbbbbb;
    padding: 10px;
    background: #fff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .tree-container {
    flex: 1;
    overflow-y: auto;
  }

  .user-list-room {
    width: 85%;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .search-row {
    margin-bottom: 20px;
  }

  .pagination-container {
    margin-top: 20px;
  }

  .custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 8px;
  }

  .el-dropdown-link {
    cursor: pointer;
    font-size: 16px;
  }

  .process-drawer-content {
    padding: 20px;

    .process-actions {
      margin-top: 20px;
      text-align: center;
    }
  }
</style>
