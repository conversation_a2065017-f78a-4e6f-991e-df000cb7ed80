<template>
  <div>
    <!-- 抽屉组件，用于新增或编辑敏感词/过滤词表 -->
    <el-drawer :before-close="handleDrawerClose" :visible.sync="drawerVisible" :with-header="false" direction="rtl" size="40%">
      <div class="drawer-container">
        <div class="header">
          <!-- 根据 importType 和 isEditing 显示标题 -->
          <h2 v-if="importType === 'sensitive'">{{ isEditing ? '编辑敏感词表' : '新增敏感词表' }}</h2>
          <h2 v-else>{{ isEditing ? '编辑过滤词表' : '新增过滤词表' }}</h2>
          <i class="el-icon-close" @click="handleDrawerClose" />
        </div>
        <el-form ref="wordListForm" :model="formData" :rules="rules" class="rule-form">
          <div class="header-content">
            <el-form-item label="" prop="name">
              <div class="name-cell">
                <span>{{ formData.name }}</span>
                <i class="el-icon-edit name-edit-icon" @click="openEditNameDialog" />
              </div>
            </el-form-item>
            <el-button type="text" @click="openAddDialog">新增</el-button>
          </div>
          <!-- 词条表格，支持无限滚动加载 -->
          <div v-show="formData.words" class="infinite-list">
            <el-table :data="formData.words" :header-cell-style="getRowClass">
              <el-table-column align="center" label="序号" type="index" width="80" />
              <el-table-column label="词条" prop="word">
                <template v-slot="{ row, $index }">
                  <div class="word-cell">
                    <!-- 显示词条内容或编辑输入框 -->
                    <span v-if="!row.isEditing" class="word-text">{{ row.name || '请输入词条' }}</span>
                    <el-input
                      v-else
                      v-model.trim="row.name"
                      class="word-input"
                      maxlength="20"
                      placeholder="请输入词条"
                      show-word-limit
                      @blur="finishEditing($index)"
                      @keyup.enter.native="finishEditing($index)"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作" width="250">
                <template v-slot="{ row, $index }">
                  <el-button :icon="row.isEditing ? '' : 'el-icon-edit'" class="edit-button" type="text" @click="toggleEdit($index, row)" />
                  <el-button icon="el-icon-delete" style="color: #ff4d4f" type="text" @click="removeWordItem($index, row)" />
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <el-row align="middle" class="pagination-container" justify="left" type="flex">
              <el-pagination
                :current-page="pagination.currentPage"
                :page-size="pagination.pageSize"
                :page-sizes="[10, 20, 30, 40, 50]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </el-row>
          </div>
          <el-form-item class="form-actions">
            <el-button @click="handleDrawerClose">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>

    <!-- 弹窗：新增或编辑单个词条 -->
    <el-dialog :before-close="handleDialogClose" :title="currentDialogText" :visible.sync="dialogVisible" width="30%">
      <el-form ref="dialogForm" :model="dialogForm" :rules="dialogRules">
        <el-form-item prop="words">
          <el-input
            v-if="importType === 'sensitive'"
            v-model="dialogForm.words"
            :placeholder="!dialogForm.id ? '请输入敏感词，用“、”隔开多个敏感词' : '请输入敏感词，仅支持单个敏感词'"
          />
          <el-input v-else v-model="dialogForm.words" :placeholder="!dialogForm.id ? '请输入过滤词，用“、”隔开多个过滤词' : '请输入过滤词，仅支持单个过滤词'" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="confirmAddWords">确定</el-button>
      </span>
    </el-dialog>

    <!-- 弹窗：编辑词表名称 -->
    <el-dialog :before-close="handleEditNameDialogClose" :visible.sync="editNameDialog" title="编辑词表名称" width="30%">
      <el-form ref="editNameForm" :model="editNameForm" :rules="nameRules">
        <el-form-item prop="name">
          <el-input v-model.trim="editNameForm.name" maxlength="50" placeholder="请输入词表名称" show-word-limit />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleEditNameDialogClose">取消</el-button>
        <el-button type="primary" @click="confirmEditName">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import sensitiveWordsApi from '@/api/riskAudit/riskSetting';
  export default {
    name: 'AddWordList',

    /**
     * @prop {Object} currentWordList - 当前词表数据，包含名称和词条列表
     * @prop {Object} tablePagination - 分页信息，包含 currentPage、pageSize 和 total
     * @prop {Boolean} isEditing - 是否为编辑模式
     * @prop {Boolean} visible - 抽屉是否可见
     * @prop {String} importType - 词表类型，'sensitive' 或 'filter'
     */
    props: {
      currentWordList: {
        type: Object,
        default: () => ({})
      },
      tablePagination: {
        type: Object,
        default: () => ({})
      },
      isEditing: {
        type: Boolean,
        default: false
      },
      visible: {
        type: Boolean,
        default: false
      },
      importType: {
        type: String,
        default: 'sensitive'
      }
    },

    /**
     * @description 组件的响应式数据
     */
    data() {
      return {
        formData: {
          name: '',
          words: []
        },
        dialogForm: {
          words: '',
          id: undefined
        },
        editNameForm: {
          name: ''
        },
        dialogVisible: false, //是否显示新增/编辑词条弹窗
        editNameDialog: false, //是否显示编辑词表名称弹窗
        isLoading: false, //是否正在加载数据
        /** @type {Object} 词表表单的校验规则 */
        rules: {
          name: [{ required: true, message: '请输入词表名称', trigger: 'blur' }],
          words: [
            { type: 'array', required: true, message: '请至少添加一个词条', trigger: 'change' },
            {
              validator: (_rule, value, callback) => {
                if (value.every((item) => !item.name || item.name.trim() === '')) {
                  callback(new Error('词条内容不能为空'));
                } else {
                  callback();
                }
              },
              trigger: 'change'
            }
          ]
        },
        /** @type {Object} 编辑词表名称弹窗的校验规则 */
        nameRules: {
          name: [
            { required: true, message: '请输入词表名称', trigger: 'blur' },
            { max: 50, message: '词表名称不能超过50个字符', trigger: 'blur' }
          ]
        },
        /** @type {Object} 新增/编辑词条弹窗的校验规则 */
        dialogRules: {
          words: []
        },
        /** @type {Object} 弹窗标题映射 */
        textMap: {
          update: '编辑敏感词',
          create: '新增敏感词',
          updateFilters: '编辑过滤词',
          createFilters: '新增过滤词'
        },
        dialogStatus: '', //当前弹窗状态
        currentDialogText: '', //当前弹窗标题
        /** @type {Object} 表格分页信息，同步父组件的 tablePagination */
        tableFromPagination: { currentPage: 1, pageSize: 10, total: 0 },
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        }
      };
    },

    /**
     * @description 计算属性
     */
    computed: {
      /**
       * @description 控制抽屉的显示状态，双向绑定父组件的 visible
       * @returns {Boolean}
       */
      drawerVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      }
    },

    /**
     * @description 监听器
     */
    watch: {
      /**
       * @description 监听 currentWordList，同步词表名称和词条数据
       */
      currentWordList: {
        immediate: true,
        deep: true,
        handler(newVal) {
          if (newVal && Object.keys(newVal).length) {
            this.formData = {
              name: newVal.sensitiveWordName || newVal.filterWordName || '',
              words: Array.isArray(newVal.words) ? newVal.words.map((word) => ({ ...word, isEditing: false })) : []
            };
          } else {
            this.formData = { name: '', words: [] };
          }
        }
      },
      /**
       * @description 监听 visible，关闭抽屉时重置表单和状态
       */
      visible(newVal) {
        if (!newVal) {
          this.resetForm();
          this.isLoading = false;
          this.tableFromPagination = { currentPage: 1, pageSize: 10, total: 0 };
        }
      },
      /**
       * @description 监听 tablePagination，同步分页信息
       */
      tablePagination: {
        deep: true,
        handler(newVal) {
          this.tableFromPagination = { ...newVal };
        }
      }
    },

    /**
     * @description 组件创建时初始化
     */
    created() {
      this.setDialogRules();
    },

    /**
     * @description 组件方法
     */
    methods: {
      setPageData(data) {
        Object.assign(this.pagination, data);
      },
      handleSizeChange(val) {
        console.log('🚀🥶💩~ val', val);
        this.$emit('size-change', val);
      },
      handleCurrentChange(val) {
        console.log('🚀🥶💩~ val', val);
        this.$emit('current-change', val);
      },

      /**
       * @description 设置表格头部样式
       * @param {Object} params - 包含 rowIndex
       * @returns {String} 样式字符串
       */
      getRowClass({ rowIndex }) {
        return rowIndex === 0 ? 'background:#f5f7fa' : '';
      },

      /**
       * @description 设置新增/编辑词条弹窗的校验规则
       * @param {String} [importType] - 词表类型，默认为 this.importType
       */
      setDialogRules(importType) {
        if (!importType) {
          importType = this.importType;
        }
        const label = importType === 'filter' ? '过滤词' : '敏感词';
        this.dialogRules.words = [
          { required: true, message: `请输入${label}`, trigger: 'blur' },
          {
            validator: (_rule, value, callback) => {
              const words = value
                .split('、')
                .map((word) => word.trim())
                .filter(Boolean);
              if (words.length === 0) {
                callback(new Error(`请输入有效的${label}`));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ];
      },

      /**
       * @description 关闭抽屉，触发父组件的 close 事件
       */
      handleDrawerClose() {
        this.$emit('close');
      },

      /**
       * @description 打开新增词条弹窗
       */
      openAddDialog() {
        this.dialogVisible = true;
        if (this.importType === 'sensitive') {
          this.currentDialogText = '新增敏感词';
        } else {
          this.currentDialogText = '新增过滤词';
        }
      },

      /**
       * @description 关闭新增/编辑词条弹窗并重置表单
       */
      handleDialogClose() {
        if (this.$refs.dialogForm) {
          this.$refs.dialogForm.resetFields();
        }
        this.dialogForm.id = '';
        this.dialogForm.words = '';
        this.dialogVisible = false;
      },

      /**
       * @description 打开编辑词表名称弹窗
       */
      openEditNameDialog() {
        this.editNameForm.name = this.formData.name;
        this.editNameDialog = true;
      },

      /**
       * @description 关闭编辑词表名称弹窗并重置表单
       */
      handleEditNameDialogClose() {
        if (this.$refs.editNameForm) {
          this.$refs.editNameForm.resetFields();
        }
        this.editNameForm.name = '';
        this.editNameDialog = false;
      },

      /**
       * @description 确认编辑词表名称，调用 API 更新并通知父组件
       */
      async confirmEditName() {
        await this.$refs.editNameForm.validate(async (valid) => {
          if (!valid) return;
          if (this.editNameForm.name.trim() === '') {
            this.$message.error('词表名称不能为空');
            return;
          }
          try {
            let updatedWordList = { ...this.currentWordList };
            if (this.importType === 'sensitive') {
              const params = {
                sensitiveWordCode: this.currentWordList.sensitiveWordCode,
                sensitiveWordName: this.editNameForm.name
              };
              const { code } = await sensitiveWordsApi.updateSensitiveWords(params);
              if (code === 20000) {
                this.formData.name = this.editNameForm.name;
                updatedWordList.sensitiveWordName = this.editNameForm.name;
                this.$message.success('编辑成功');
                this.$emit('editSuccess', updatedWordList);
                this.handleEditNameDialogClose();
              }
            } else {
              const params = {
                filterWordCode: this.currentWordList.filterWordCode,
                filterWordName: this.editNameForm.name
              };
              const { code } = await sensitiveWordsApi.updateFilterWords(params);
              if (code === 20000) {
                this.formData.name = this.editNameForm.name;
                updatedWordList.filterWordName = this.editNameForm.name;
                this.$message.success('编辑成功');
                this.$emit('editSuccess', updatedWordList, 'filter');
                this.handleEditNameDialogClose();
              }
            }
          } catch (error) {
            this.$message.error('编辑失败，请重试');
            console.error('编辑词表名称失败:', error);
          }
        });
      },

      /**
       * @description 确认新增或编辑词条，校验后调用相应的 API
       */
      confirmAddWords() {
        this.$refs.dialogForm.validate((valid) => {
          if (!valid) return;
          const newWords = this.dialogForm.words.split('、');
          if (newWords.length === 0) {
            this.$message.error('请输入有效的敏感词');
            return;
          }
          const existingWords = this.formData.words.filter((item) => item.id !== this.dialogForm.id).map((item) => item.name);
          const label = this.importType === 'filter' ? '过滤词' : '敏感词';
          const isEditMode = !!this.dialogForm.id;
          if (!this.validateWords(newWords, existingWords, isEditMode, label)) {
            return;
          }
          if (this.importType === 'filter') {
            isEditMode ? this.editWordFilterItem(this.dialogForm.words) : this.addWordFilterItem(this.dialogForm.words);
          } else {
            isEditMode ? this.editWordItem(this.dialogForm.words) : this.addWordItem(this.dialogForm.words);
          }
          this.$refs.wordListForm.validateField('words');
          this.handleDialogClose();
        });
      },

      /**
       * @description 校验新增或编辑的词条是否有效
       * @param {String[]} newWords - 新输入的词条数组
       * @param {String[]} existingWords - 已存在的词条数组
       * @param {Boolean} isEditMode - 是否为编辑模式
       * @param {String} label - 词条类型标签（敏感词/过滤词）
       * @returns {Boolean} 是否通过校验
       */
      validateWords(newWords, existingWords, isEditMode, label) {
        if (isEditMode) {
          const word = this.dialogForm.words;
          if (word.length > 20) {
            this.$message.error(`${label}长度不得超过20个字`);
            return false;
          }
          if (existingWords.includes(word)) {
            this.$message.warning(`词条“${word}”已存在`);
            return false;
          }
        } else {
          const invalidWords = newWords.filter((word) => word.length > 20);
          if (invalidWords.length > 0) {
            this.$message.error(`每个${label}不得超过20个字`);
            return false;
          }
          const duplicates = newWords.filter((word) => existingWords.includes(word));
          if (duplicates.length > 0) {
            this.$message.warning(`以下词条已存在：${duplicates.join('、')}`);
            return false;
          }
        }
        return true;
      },

      /**
       * @description 添加敏感词条，调用 API 并通知父组件
       * @param {String} words - 新增的词条内容
       */
      async addWordItem(words) {
        try {
          const { code } = await sensitiveWordsApi.addSensitiveWordsItem({
            name: words,
            sensitiveWordCode: this.currentWordList.sensitiveWordCode
          });
          if (code === 20000) {
            this.$message.success('添加成功');
            this.$emit('editSuccess', this.currentWordList);
          }
        } catch (error) {
          this.$message.error('添加失败，请重试');
          console.error('添加敏感词失败:', error);
        }
      },

      /**
       * @description 编辑敏感词条，调用 API 并通知父组件
       * @param {String} words - 编辑后的词条内容
       */
      async editWordItem(words) {
        try {
          const { code } = await sensitiveWordsApi.updateSensitiveWordsItem({
            name: words,
            id: this.dialogForm.id
          });
          if (code === 20000) {
            this.$message.success('编辑成功');
            this.$emit('editSuccess', this.currentWordList);
          }
        } catch (error) {
          this.$message.error('编辑失败，请重试');
          console.error('编辑敏感词失败:', error);
        }
      },

      /**
       * @description 编辑过滤词条，调用 API 并通知父组件
       * @param {String} words - 编辑后的词条内容
       */
      async editWordFilterItem(words) {
        try {
          const { code } = await sensitiveWordsApi.updateFilterWordsItem({
            name: words,
            id: this.dialogForm.id
          });
          if (code === 20000) {
            this.$message.success('编辑成功');
            this.$emit('editSuccess', this.currentWordList, 'filter');
          }
        } catch (error) {
          this.$message.error('编辑失败，请重试');
          console.error('编辑过滤词失败:', error);
        }
      },

      /**
       * @description 添加过滤词条，调用 API 并通知父组件
       * @param {String} words - 新增的词条内容
       */
      async addWordFilterItem(words) {
        try {
          const { code } = await sensitiveWordsApi.addFilterWordsItem({
            name: words,
            FilterWordCode: this.currentWordList.filterWordCode
          });
          if (code === 20000) {
            this.$message.success('添加成功');
            this.$emit('editSuccess', this.currentWordList, 'filter');
          }
        } catch (error) {
          this.$message.error('添加失败，请重试');
          console.error('添加过滤词失败:', error);
        }
      },

      /**
       * @description 切换词条的编辑状态，打开编辑弹窗
       * @param {Number} index - 词条索引
       * @param {Object} row - 词条数据
       */
      toggleEdit(index, row) {
        this.dialogStatus = 'update';
        this.dialogVisible = true;
        this.dialogForm.words = row.name;
        this.dialogForm.id = row.id;
        if (this.importType === 'sensitive') {
          this.currentDialogText = '编辑敏感词';
        } else {
          this.currentDialogText = '编辑过滤词';
        }
      },

      /**
       * @description 完成词条的 inline 编辑，校验并更新数据
       * @param {Number} index - 词条索引
       */
      finishEditing(index) {
        const word = this.formData.words[index].name;
        if (word) {
          const existingWords = this.formData.words.filter((_, i) => i !== index).map((item) => item.name);
          if (existingWords.includes(word)) {
            this.$message.warning(`词条“${word}”已存在`);
            return;
          }
          this.formData.words[index].name = word;
          this.formData.words[index].isEditing = false;
        }
        this.$refs.wordListForm.validateField('words');
      },

      /**
       * @description 删除词条，调用 API 并通知父组件
       * @param {Number} index - 词条索引
       * @param {Object} row - 词条数据
       */
      async removeWordItem(index, row) {
        const label = this.importType === 'filter' ? '过滤词' : '敏感词';
        this.$confirm(`是否永久删除${label}“ ${row.name} ” ？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.formData.words.splice(index, 1);
          this.$refs.wordListForm.validateField('words');
          try {
            if (this.importType === 'sensitive') {
              const res = await sensitiveWordsApi.deleteSensitiveWordsItem(row.id);
              if (res.code === 20000) {
                this.$message.success('删除成功');
                this.$emit('editSuccess', this.currentWordList);
              }
            } else {
              const res = await sensitiveWordsApi.deleteFilterWordsItem(row.id);
              if (res.code === 20000) {
                this.$message.success('删除成功');
                this.$emit('editSuccess', this.currentWordList, 'filter');
              }
            }
          } catch (error) {
            this.$message.error('删除失败，请重试');
            console.error('删除词条失败:', error);
          }
        });
      },

      /**
       * @description 保存词表，校验后触发父组件的 save 事件
       */
      saveWordList() {
        this.$refs.wordListForm.validate((valid) => {
          if (valid) {
            const wordCount = this.formData.words.filter((w) => w.name.trim()).length;
            const updatedItem = {
              ...this.currentWordList,
              name: this.formData.name,
              words: this.formData.words.map((item) => item.name).join(','),
              wordCount
            };
            this.$emit('save', updatedItem);
            this.handleDrawerClose();
          }
        });
      },

      /**
       * @description 重置表单数据
       */
      resetForm() {
        this.formData = { name: '', words: [] };
        if (this.$refs.wordListForm) {
          this.$refs.wordListForm.resetFields();
        }
      },

      /**
       * 重置分页
       */
      changeCurrentPage() {
        this.tableFromPagination.currentPage = 1;
      }
    }
  };
</script>

<style lang="scss" scoped>
  /** @description 抽屉容器样式，设置内边距和全屏高度 */
  .drawer-container {
    padding: 20px;
    height: 100vh;
  }

  /** @description 头部样式，标题和关闭按钮的布局 */
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /** @description 头部标题样式 */
  .header h2 {
    font-size: 18px;
    color: #333;
  }

  /** @description 关闭图标样式 */
  .el-icon-close {
    font-size: 24px;
    cursor: pointer;
  }

  /** @description 表单容器样式，设置背景、边距和布局 */
  .rule-form {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;

    /** @description 头部内容区域，包含词表名称和新增按钮 */
    .header-content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      line-height: 40px;
    }

    /** @description 无限滚动列表区域，设置自适应高度和滚动 */
    .infinite-list {
      flex: 1;
      overflow-y: auto;
    }
  }

  /** @description 输入框样式，设置全宽 */
  .el-input {
    width: 100%;
  }

  /** @description 文本按钮样式，设置颜色 */
  .el-button--text {
    color: #409eff;
  }

  /** @description 文本按钮悬停效果 */
  .el-button--text:hover {
    text-decoration: underline;
  }

  /** @description 表单操作按钮区域，右对齐 */
  .form-actions {
    text-align: right;
    margin-top: 20px;
  }

  /** @description 表格样式，设置全宽和底部外边距 */
  .el-table {
    width: 100%;
    margin-bottom: 20px;
  }

  /** @description 表格单元格内边距 */
  .el-table th,
  .el-table td {
    padding: 8px 0;
  }

  /** @description 词表名称单元格，包含名称和编辑图标 */
  .name-cell {
    display: flex;
    align-items: center;
    width: 100%;
  }

  /** @description 编辑图标样式 */
  .name-edit-icon {
    font-size: 16px;
    color: #409eff;
    cursor: pointer;
    margin-left: 8px;
    &:hover {
      color: #66b1ff;
    }
  }

  /** @description 词条单元格，包含词条文本或输入框 */
  .word-cell {
    display: flex;
    align-items: center;
    width: 100%;
  }

  /** @description 词条文本样式，设置溢出省略 */
  .word-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /** @description 词条编辑输入框样式 */
  .word-input {
    flex: 1;
    margin-right: 8px;
    :deep(.el-input__inner) {
      height: 28px;
      line-height: 28px;
      border: none !important;
      border-bottom: 1px solid #dcdfe6 !important;
      border-radius: 0 !important;
      padding: 0 !important;
    }
    :deep(.el-input__inner:focus) {
      border-bottom: 1px solid #409eff !important;
    }
  }

  /** @description 编辑按钮样式 */
  .edit-button {
    font-size: 16px;
    color: #409eff;
    margin-left: 8px;
  }

  /** @description 表格中的文本按钮样式 */
  .el-table .el-button--text {
    font-size: 16px;
    margin: 0 5px;
  }
</style>
