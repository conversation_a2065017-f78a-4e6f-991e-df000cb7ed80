/** When your routing table is too long, you can split it into small modules**/

import Layout from '@/layout';
const toolRouter = {
  path: '/toolView',
  component: Layout,
  name: 'toolView',
  meta: {
    title: '工具',
    icon: 'chart',
    perm: 'm:toolView'
  },
  alwaysShow: true,
  children: [
    {
      path: 'groupInherit',
      component: () => import('@/views/toolView/groupInherit'),
      name: 'groupInherit',
      meta: { title: '群继承', noCache: true, affix: false, perm: 'm:toolView:groupInherit', icon: 'studentList' }
    }
  ]
};

export default toolRouter;
