/**
 * 忘记密码相关接口
 */
import request from '@/utils/request';

const prefix = 'im';

export default {
  // //发送短信
  // sendSmg(mobile) {
  //   return request({
  //     url: '/znyy/user/sms/midify/pwd/' + mobile,
  //     method: 'POST'
  //   });
  // },

  //发送短信
  sendSmg(mobile, code) {
    return request({
      url: '/new/security/sms/back/forget',
      method: 'POST',
      data: {
        mobile,
        code
      }
    });
  },
  // 重置密码
  pswReset(data) {
    return request({
      // url: '/znyy/user/merchant/update/pwd',
      url: prefix + '/api/sys-user/change-pwd',
      method: 'PUT',
      data
    });
  }
};
