/**
 * 风险设置相关接口
 */
import request from '@/utils/request';
const prefix = 'im';

export default {
  /**
   * 风险设置列表
   * @param data
   * @returns {*}
   */
  settingList(data) {
    return request({
      // url: prefix + '/risk/rules',
      url: prefix + '/risk/rules/list',
      method: 'get',
      params: data
    });
  },

  /**
   * 查看详情
   */
  getSettingDetail(id) {
    return request({
      url: prefix + '/risk/rules/detail?id=' + id,
      method: 'get'
    });
  },

  /**
   * 新增风险设置
   * @param data
   * @returns {*}
   */
  addSetting(data) {
    return request({
      url: prefix + '/risk/rules',
      method: 'post',
      data
    });
  },
  /**
   * 删除风险设置
   * @param id
   * @returns {*}
   */
  deleteSetting(id) {
    return request({
      url: prefix + '/risk/rules?id=' + id,
      method: 'delete'
    });
  },
  /**
   * 修改风险设置
   * @param data
   * @returns {*}
   */
  updateSetting(data) {
    return request({
      url: prefix + '/risk/rules',
      method: 'put',
      params: data
    });
  },

  /**
   * 敏感词列表
   * @param data
   * @returns {*}
   */
  sensitiveWordsList(data) {
    return request({
      url: prefix + '/risk/sensitive/words',
      method: 'get',
      params: data
    });
  },

  sensitiveWordsListApi(data) {
    return request({
      url: prefix + '/risk/sensitive/words/list',
      method: 'get',
      params: data
    });
  },

  /**
   *导入敏感词
   * @param data
   * @returns {*}
   */
  importSensitiveWords(data) {
    return request({
      url: prefix + '/risk/sensitive/words/import',
      method: 'post',
      data
    });
  },

  /**
   * 删除敏感词
   * @param id
   * @returns {*}
   */
  deleteSensitiveWords(id) {
    return request({
      url: prefix + '/risk/sensitive/words?sensitiveWordId=' + id,
      method: 'delete'
    });
  },

  /**
   * 复制敏感词表
   * @param id
   * @returns {*}
   */
  copySensitiveWords(id) {
    return request({
      url: prefix + '/risk/sensitive/words/copy?sensitiveWordId=' + id,
      method: 'post'
    });
  },
  /**
   * 修改敏感词
   * @param data
   * @returns {*}
   */
  updateSensitiveWords(data) {
    return request({
      url: prefix + '/risk/sensitive/words',
      method: 'put',
      params: data
    });
  },

  /**
   * 获取敏感词条目
   */
  getSensitiveWordsItem(data) {
    return request({
      url: prefix + '/risk/sensitive/items',
      method: 'get',
      params: data
    });
  },
  /**
   * 删除敏感词条目
   * @param id
   * @returns {*}
   */
  deleteSensitiveWordsItem(id) {
    return request({
      url: prefix + '/risk/sensitive/items?id=' + id,
      method: 'delete'
    });
  },

  /**
   * 编辑敏感词条目
   */
  updateSensitiveWordsItem(data) {
    return request({
      url: prefix + '/risk/sensitive/items',
      method: 'put',
      params: data
    });
  },
  /**
   * 新增敏感词条目
   */
  addSensitiveWordsItem(data) {
    return request({
      url: prefix + '/risk/sensitive/items',
      method: 'post',
      params: data
    });
  },

  /**
   * 获取过滤词表
   *
   */
  getFilterWords(data) {
    return request({
      url: prefix + '/risk/filter/words',
      method: 'get',
      params: data
    });
  },

  getFilterWordsApi(data) {
    return request({
      url: prefix + '/risk/filter/words/list',
      method: 'get',
      params: data
    });
  },

  /**
   * 删除过滤词
   * @param filterWordId
   * @returns {*}
   */
  deleteFilterWords(filterWordId) {
    return request({
      url: prefix + '/risk/filter/words?filterWordId=' + filterWordId,
      method: 'delete'
    });
  },

  /**
   * 复制过滤词表
   * @param filterWordId
   * @returns {*}
   */
  copyFilterWords(filterWordId) {
    return request({
      url: prefix + '/risk/filter/words/copy?filterWordId=' + filterWordId,
      method: 'post'
    });
  },

  /**
   * 编辑过滤词
   */
  updateFilterWords(data) {
    return request({
      url: prefix + '/risk/filter/words',
      method: 'put',
      params: data
    });
  },
  /**
   * 导入过滤词
   */
  importFilterWords(data) {
    return request({
      url: prefix + '/risk/filter/words/import',
      method: 'post',
      data
    });
  },
  /**
   * 获取过滤词条目
   */
  getFilterWordsItem(data) {
    return request({
      url: prefix + '/risk/filte/items',
      method: 'get',
      params: data
    });
  },
  /**
   * 删除过滤词条目
   * @param id
   * @returns {*}
   */
  deleteFilterWordsItem(id) {
    return request({
      url: prefix + '/risk/filte/items?id=' + id,
      method: 'delete'
    });
  },
  /**
   * 编辑过滤词条目
   */
  updateFilterWordsItem(data) {
    return request({
      url: prefix + '/risk/filte/items',
      method: 'put',
      params: data
    });
  },
  /**
   * 新增过滤词条目
   */
  addFilterWordsItem(data) {
    return request({
      url: prefix + '/risk/filte/items',
      method: 'post',
      params: data
    });
  },
  /**
   * 获取监控对象
   */
  getMonitorObject() {
    return request({
      url: prefix + '/risk/rules/scope',
      method: 'get'
    });
  },
  /**
   * 查询业务角色列表
   */
  getRoles(data) {
    return request({
      url: prefix + '/api/role/get-roles-List',
      method: 'get',
      params: data
    });
  },
  /**
   * 获取单聊人员
   */

  getChatUserListSingle(data) {
    return request({
      url: prefix + '/api/user/listByTenantIds',
      method: 'post',
      data
    });
  },

  /**
   * 新增敏感词
   */

  addSensitiveWords(data) {
    // 将 data 对象转换为查询字符串
    const queryString = Object.keys(data)
      .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
      .join('&');
    // 拼接 URL 和查询字符串
    const urlWithParams = prefix + `/risk/sensitive/words/add-glossary?${queryString}`;
    // 发送 POST 请求，params 为空（参数已拼接到 URL）
    return request({
      url: urlWithParams,
      method: 'post',
      data: {} // 如果 POST 请求不需要 body，可以设置为空对象
    });
  },
  /**
   * 新增过滤词
   */
  addFilterWords(data) {
    // 将 data 对象转换为查询字符串
    const queryString = Object.keys(data)
      .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
      .join('&');
    // 拼接 URL 和查询字符串
    const urlWithParams = prefix + `/risk/filter/words/add-glossary?${queryString}`;
    // 发送 POST 请求，params 为空（参数已拼接到 URL）
    return request({
      url: urlWithParams,
      method: 'post',
      data: {} // 如果 POST 请求不需要 body，可以设置为空对象
    });
  },

  /**
   * 敏感词表列表（新-分页）
   */
  getSensitiveWordsListApi(data) {
    return request({
      url: prefix + '/risk/sensitive/items/get-sensitive-words',
      method: 'get',
      params: data
    });
  },
  /**
   * 过滤词表列表（新-分页）
   */
  getFilterWordsListApi(data) {
    return request({
      url: prefix + '/risk/filte/items/word-list',
      method: 'get',
      params: data
    });
  }
};
