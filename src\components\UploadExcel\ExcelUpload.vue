<!-- 上传excel文件，目前仅适用于考题管理的批量导入(不自动上传) -->
<template>
  <div>
    <!--
      :http-request="uploadHttp" -->
    <el-upload
      action=""
      v-loading="loading"
      ref="upload"
      :http-request="uploadHttp"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :on-remove="handleRemove"
      name="file"
      drag
      :show-file-list="false"
      :auto-upload="false"
      :on-change="onSuccess"
      :disabled="fileList.length != 0"
    >
      <i class="el-icon-upload" />
      <div class="el-upload__text" v-if="fileList.length == 0">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
      <div class="el-upload__text" v-else>
        <div v-for="item in fileList" :key="item.uid">
          <span class="name">{{ item.name }}</span>
          <el-button type="text" style="margin-left: 10px" @click.stop="handleRemove(item)">删除</el-button>
        </div>
      </div>

      <div class="el-upload__text">
        <span>仅支持格式为 .xls 和 .xlsx 的Excel文件</span>
      </div>
    </el-upload>
  </div>
</template>

<script>
  // import imageApi from '@/api/paper/train/image'
  import sensitiveWordsApi from '@/api/riskAudit/riskSetting';

  export default {
    name: 'ExcelUpload',
    props: {
      //点击错误提示信息
      errMsg: {
        type: String,
        default: undefined
      },
      // 文件上传数量
      limit: {
        type: [Number, String],
        default: 1
      },
      // 文件大小尺寸
      imgSize: {
        type: Number,
        default: 5 * 1024 * 1024 // 5M=>5*1024*1024 500KB=>500*1024
      },
      // 是否显示文件的tip
      showTip: {
        type: Boolean,
        default: true
      },
      // 展示的文件列表
      fileList: {
        type: Array,
        default() {
          return [];
        }
      },
      fullUrl: {
        type: Boolean,
        default: false
      },
      dialogVisible: false
    },
    data() {
      return {
        loading: false
      };
    },
    computed: {
      // 动态显示MB或者KB
      isKbOrMb() {
        return this.imgSize / 1024 / 1024 >= 1 ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB` : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
      }
    },
    created() {
      // ossPrClient();
    },
    methods: {
      // 文件列表变化
      onSuccess(response) {
        const validateState = this.handleBeforeUpload(response);
        if (validateState) {
          this.$emit('handleSuccess', response || '');
        }
      },
      // 文件删除
      handleRemove(file) {
        console.log('前===', this.fileList);
        var index = this.fileList.findIndex((item) => {
          if (item.uid === file.uid) {
            return true;
          }
        });
        // this.fileList.splice(index, 1);
        // console.log("后===",this.fileList)
        this.$emit('handleRemove', index);
      },
      uploadHttp({ file }) {
        this.loading = true;
        const formData = new FormData();
        formData.append('file', file);
        sensitiveWordsApi
          .importSensitiveWords(formData)
          .then((res) => {
            console.log('🚀🥶💩11~ res', res);
            if (res.success) {
              res.data.forEach((a) => {
                this.fileList.push({ uid: file.uid, url: a });
                this.handleSuccess(a);
              });
            }
            this.loading = false;
          })
          .catch((e) => {
            this.$message.error('上传文件失败请检查网络或者刷新页面');
            this.loading = false;
            this.fileList = [];
            this.handleSuccess();
          });
        const fileName = 'manage/' + Date.parse(new Date());
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传文件成功回调1`, res, url, name);
              this.fileList.push({ uid: file.uid, url: this.aliUrl + name });
              if (this.fullUrl) {
                this.handleSuccess(this.aliUrl + name);
              } else {
                this.handleSuccess(name);
              }
              this.loading = false;
            }
          })
          .catch((err) => {
            this.$message.error('上传文件失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传文件失败回调`, err);
          });
      },
      /* 上传文件开始 */
      // 文件上传之前的校验
      handleBeforeUpload(file) {
        if (this.errMsg) {
          this.$message.error(this.errMsg);
          return false;
        }
        const isXLS = file.type === 'application/vnd.ms-excel' || file.name.endsWith('.xls') || file.name.endsWith('.xlsx');

        const isSize = file.size < this.imgSize; // 文件是否小于限定的尺寸

        if (!isXLS) {
          this.$message.error('上传的文件只能是 XLS、XLSX 格式');
          return false;
        }

        // 文件是否小于限定的尺寸
        if (!isSize) {
          this.$message.error(`上传的图片大小不能超过 ${this.isKbOrMb}`);
          return false;
        }
        return isXLS && isSize;
      },
      // 文件上传失败
      handleError(err) {
        // "文件上传失败"
        this.$message.error(err);
        console.log(err);
      }
      // 文件超过上传个数
      // onExceed() {
      //   this.$message.error(`最多只能上传 ${this.limit} 个文件`);
      //   return false;
      // },
      // 文件上传成功
      // handleSuccess(res) {
      // this.$emit("handleSuccess", res || "");
      // },
      /* 上传文件结束 */
    }
  };
</script>

<style scoped>
  ::v-deep .el-upload {
    width: 100%;
  }
</style>
