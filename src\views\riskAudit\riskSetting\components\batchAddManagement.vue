<template>
  <div class="batch-add-questions">
    <el-dialog title="批量导入词表" :visible.sync="dialogOpen" width="40%" :before-close="handleClose" v-loading="importLoading">
      <div class="dialog-content">
        <excel-upload :limit="1" :showTip="false" :fileList="fileList" @handleSuccess="handleSuccess" @handleRemove="handleRemove"></excel-upload>
        <!-- 导入说明 -->
        <p class="desc">导入说明</p>
        <p class="desc-list">
          <span class="index">1.</span>
          <a :href="downloadUrl" download target="_blank" style="text-decoration: none">
            <el-button type="text" size="small" v-loading="exportLoading">下载模板</el-button>
          </a>

          <!--          <el-button type="text" size="small" v-loading="exportLoading" @click="importTemplate">下载模板</el-button>-->
          <!-- <el-link href="/template/questionTemplate.xlsx" :underline="false" download="批量导入考题模板.xlsx" target="_blank" style="color: #1890ff;">下载模板</el-link> -->
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-loading="importLoading" @click="importQuestion">导入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import ExcelUpload from '@/components/UploadExcel/ExcelUpload.vue';
  // import examApi from '@/api/training/exam';
  import sensitiveWordsApi from '@/api/riskAudit/riskSetting';
  export default {
    name: 'batchAddManagement',
    components: {
      ExcelUpload
    },
    props: {
      dialogOpen: {
        type: Boolean,
        default: false
      },
      importType: {
        type: String,
        default: 'sensitive'
      }
    },
    emits: ['updateTable', 'closeBatchDialog'],

    data() {
      return {
        fileList: [],
        exportLoading: false,
        tempFile: null,
        importLoading: false,
        downloadUrl: 'https://document.dxznjy.com/scrmRes/敏感词导入模板.xlsx'
      };
    },

    methods: {
      setDownLoadUrl(val) {
        this.downloadUrl = val;
      },

      handleClose() {
        this.$emit('closeBatchDialog');
        this.fileList = [];
      },
      handleSuccess(res) {
        this.fileList.push(res);
        this.tempFile = res;
      },
      handleRemove(index) {
        if (index != -1) {
          this.fileList.splice(index, 1);
          this.tempFile = null;
          console.log('后===', this.fileList);
        }
      },
      // 下载模板
      importTemplate() {
        const that = this;
        that.exportLoading = true;
        examApi
          .importTemplate({})
          .then((response) => {
            // 创建一个blob对象，供浏览器使用
            const blob = new Blob([response]);

            console.log(response);
            const url = window.URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', '批量导入考题模板.xls');
            document.body.appendChild(link);
            link.click();

            // 移除链接并释放内存
            link.parentNode.removeChild(link);
            window.URL.revokeObjectURL(url);
            that.exportLoading = false;
          })
          .catch((err) => {
            console.log('下载文件时出错', err);
            this.$notify.error('下载文件出错，请重新下载');
          });
      },
      // 批量导入
      importQuestion() {
        if (!this.tempFile) {
          this.$message.error(`请先上传配置好的导入文件`);
          return false;
        } else {
          // 导入
          const that = this;
          that.importLoading = true;
          const formData = new FormData();
          formData.append('file', that.tempFile.raw);

          if (that.importType === 'sensitive') {
            sensitiveWordsApi
              .importSensitiveWords(formData)
              .then((res) => {
                this.$message.success('导入成功');
                this.fileList = [];
                this.tempFile = null;
                this.importLoading = false;
                this.$emit('updateTable');
              })
              .catch((err) => {
                console.log(err, '-------------------------');
                that.$message.error('导入失败，请重试！');
                that.importLoading = false;
              });
          } else {
            sensitiveWordsApi
              .importFilterWords(formData)
              .then((res) => {
                that.$message.success(res.message);
                that.fileList = [];
                that.tempFile = null;
                that.importLoading = false;
                that.$emit('updateTable');
              })
              .catch((err) => {
                // that.$message.error('导入失败，请重试！')
                that.importLoading = false;
              });
          }
        }
      }
    }
  };
</script>

<style scoped>
  ::v-deep .el-upload.el-upload--picture,
  ::v-deep .el-upload-dragger {
    width: 100%;
  }

  ::v-deep .el-dialog__body {
    padding: 30px 50px;
  }

  .dialog-footer {
    text-align: center;
  }
</style>
