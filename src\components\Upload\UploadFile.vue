<template>
  <div>
    <el-upload
      action
      v-loading="loading"
      :http-request="uploadHttp"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleError"
      :on-exceed="onExceed"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      name="file"
    >
      <div class="el-upload__text">
        <el-button size="small" type="primary" style>点击上传</el-button>
      </div>
    </el-upload>
  </div>
</template>

<script>
  import { ossPrClient } from '@/api/system/alibabaalibaba';

  export default {
    name: 'UploadFile',
    props: {
      // 图片大小尺寸
      imgSize: {
        type: Number,
        default: 5 * 1024 * 1024 // 5M=>5*1024*1024 500KB=>500*1024
      },
      // 展示的图片列表
      fileList: {
        type: Array,
        default() {
          return [];
        }
      },
      limit: {
        type: Number,
        default: 1
      },
      dialogVisible: false
    },
    data() {
      return {
        loading: false
      };
    },
    computed: {
      // 动态显示MB或者KB
      isKbOrMb() {
        return this.imgSize / 1024 / 1024 >= 1 ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB` : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
      }
    },
    created() {
      ossPrClient();
    },
    methods: {
      beforeUpload(file) {
        this.$emit('beforeUpload', file);
      },
      uploadHttp({ file }) {
        this.loading = true;
        const fileName = 'paper/' + Date.parse(new Date()) + file.name.substr(file.name.lastIndexOf('.'));
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              this.fileList.push({ name: file.name, uid: file.uid, url: url });
              this.handleSuccess(this.aliUrl + name, file.name, file);
              this.loading = false;
            }
          })
          .catch((err) => {
            this.$message.error('上传图片失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      },
      /* 上传图片开始 */
      // 图片上传失败
      handleError(err) {
        this.$message.error('图片上传失败');
        console.log(err);
      },
      // 文件超过上传个数
      onExceed() {
        return false;
      },
      // 图片删除
      handleRemove(file) {
        var index = this.fileList.findIndex((item) => {
          if (item.uid === file.uid) {
            return true;
          }
        });
        this.fileList.splice(index, 1);
        this.$emit('handleRemove', file);
      },
      // 图片上传成功
      handleSuccess(res, fileName, file) {
        console.log(res);
        console.log('----------------------------------------------------------');
        this.$emit('handleSuccess', res || '', fileName || '', file);
      }
      /* 上传图片结束 */
    }
  };
</script>
