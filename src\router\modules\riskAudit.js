import Layout from '@/layout';

const riskAudit = {
  path: '/riskAudit',
  meta: {
    title: '风险审计',
    icon: 'chart',
    perm: 'm:risk'
  },
  component: Layout,
  alwaysShow: true,
  children: [
    {
      path: 'riskRecording',
      component: () => import('@/views/riskAudit/riskRecording/index'),
      meta: { title: '风险记录', icon: 'system_figuration', affix: false, perm: 'm:risk:riskRecording' },
      name: 'riskRecording'
    },
    {
      path: 'riskSetting',
      component: () => import('@/views/riskAudit/riskSetting/index.vue'),
      name: 'riskSetting',
      meta: { title: '风险设置', icon: 'system_figuration', affix: false, perm: 'm:risk:riskSetting' }
      // children: [
      //   {
      //     path: 'sensitiveWordsList',
      //     component: () => import('@/views/riskAudit/riskSetting/sensitiveWordsList.vue'),
      //     name: 'SensitiveWordsList',
      //     meta: { title: '敏感词表', icon: 'system_figuration', affix: false },
      //     hidden: true
      //   }
      // ]
    },
    {
      path: 'sensitiveWordsList',
      component: () => import('@/views/riskAudit/riskSetting/sensitiveWordsList.vue'),
      name: 'SensitiveWordsList',
      meta: { title: '敏感词表', icon: 'system_figuration', affix: false, perm: 'm:risk:sensitiveWordsList' },
      hidden: true
    },
    {
      path: 'complaintManage',
      component: () => import('@/views/riskAudit/complaintManage/index.vue'),
      name: 'complaintManage',
      meta: { title: '投诉管理', icon: 'system_figuration', affix: false, perm: 'm:risk:complaintManage' }
    }
  ]
};

export default riskAudit;
