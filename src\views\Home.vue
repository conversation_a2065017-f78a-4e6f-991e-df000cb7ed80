<template>
  <div class="home">
    <div class="content">
      <div class="logo-container">
        <div class="logo-circle"></div>
        <h1 class="title">IM中台</h1>
      </div>
      <p class="subtitle">安徽鼎校智能教育</p>
      <div class="features">
        <div class="feature-item">
          <i class="el-icon-chat-dot-round"></i>
          <span>实时通讯</span>
        </div>
        <div class="feature-item">
          <i class="el-icon-data-line"></i>
          <span>数据分析</span>
        </div>
        <div class="feature-item">
          <i class="el-icon-set-up"></i>
          <span>安全可靠</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'Home'
  };
</script>

<style lang="scss" scoped>
  .home {
    width: 100%;
    height: 91vh;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    }

    .content {
      text-align: center;
      animation: fadeIn 1s ease-in-out;
      z-index: 1;
    }

    .logo-container {
      position: relative;
      display: inline-block;
      margin-bottom: 20px;
    }

    .logo-circle {
      width: 120px;
      height: 120px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      animation: pulse 2s infinite;
    }

    .title {
      font-size: 48px;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      font-weight: 500;
      letter-spacing: 2px;
      margin: 0;
      position: relative;
    }

    .subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 20px;
      margin: 20px 0 40px;
      font-weight: 300;
    }

    .features {
      display: flex;
      justify-content: center;
      gap: 40px;
      margin-top: 40px;
    }

    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #fff;
      transition: transform 0.3s ease;

      &:hover {
        transform: translateY(-5px);
      }

      i {
        font-size: 32px;
        margin-bottom: 10px;
      }

      span {
        font-size: 16px;
        opacity: 0.9;
      }
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.5;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.1);
      opacity: 0.2;
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.5;
    }
  }
</style>
