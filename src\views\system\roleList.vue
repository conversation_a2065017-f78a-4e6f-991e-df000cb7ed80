<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="角色名称：">
        <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px">
      <el-button type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
    </el-form>

    <el-table
      class="period-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="loginName" label="角色名称"></el-table-column>
      <el-table-column prop="nickName" label="角色标识" width="180"></el-table-column>
      <el-table-column prop="id" label="操作" sortable width="200">
        <template>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
          <el-button type="warning" size="mini">分配权限</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="totalBonus" label="是否启用" show-overflow-tooltip>
        <template>
          <span class="green">是</span>
          <span class="red">否</span>
        </template>
      </el-table-column>
      <el-table-column prop="loginName" label="是否是内置角色">
        <template>
          <span class="green">是</span>
          <span class="red">否</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加弹窗 -->
    <el-dialog :title="addOrUpdate ? '添加用户角色' : '编辑用户角色'" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false" @close="close">
      <el-form
        :ref="addOrUpdate ? 'addCourseData' : 'updateActive'"
        :rules="rules"
        :model="addOrUpdate ? addCourseData : updateActive"
        label-position="left"
        label-width="120px"
        style="width: 50%"
      >
        <el-form-item label="角色名称:">
          <el-input v-model="addCourseData.classDescription" />
        </el-form-item>
        <el-form-item label="角色编码:">
          <el-input v-model="addCourseData.classDescription" />
        </el-form-item>
        <el-form-item label="是否商户角色:" prop="isEnable">
          <el-checkbox></el-checkbox>
        </el-form-item>
        <el-form-item label="是否启用:" prop="isEnable">
          <el-checkbox></el-checkbox>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateActive')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import { pageParamNames } from '@/utils/constants';
  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [
          {
            name: 1,
            title: '1111'
          }
        ],
        dataQuery: {},
        dialogVisible: false,
        addOrUpdate: true,
        updateActive: {},
        addCourseData: {},
        showLoginAccount: false
      };
    },
    created() {
      // this.fetchData();
    },
    methods: {
      // 查询提现列表
      fetchData() {
        const that = this;
        //that.tableLoading = true
        queryOfficialAccountLink(that.tablePage.currentPage, that.tablePage.size).then((res) => {
          console.log(res);
          that.tableData = res.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      //新增操作
      clickAdd() {
        this.dialogVisible = true;
      },
      // 点击编辑按钮
      handleUpdate(id) {
        const that = this;
        that.dialogVisible = true;
        that.addOrUpdate = false;
        courseApi
          .queryActive(id)
          .then((res) => {
            that.updateActive = res.data;
            that.radio = that.updateActive.isEnable.toString(); //状态回显
            // console.log(that.updateActive)
            if (that.updateActive.icon !== null && that.updateActive.icon.length > 1) {
              that.fileDetailList = [
                {
                  url: that.aliUrl + that.updateActive.icon
                }
              ];
            } else {
              that.fileDetailList = [];
            }
          })
          .catch((err) => {});
      },
      // 打开修改登陆账号
      openLogin() {
        this.showLoginAccount = true;
      },
      closeLogin() {
        this.showLoginAccount = false;
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .period-table td,
  .period-table th {
    text-align: center;
  }
  .mt20 {
    margin-top: 20px;
  }
</style>
