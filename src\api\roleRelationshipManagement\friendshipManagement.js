/**
 * 会话存档接口
 */
import request from "@/utils/request";
/**
  * 获取聊天角色列表
  * @param data
  * @returns {*}
  */
export const getChatRoleList = (data) => {
  return request({
    url: "/im/api/role/get-roles",
    method: "GET",
    params: data
  });
}
/**
  * 获取好友关系组列表
  * @param data
  * @returns {*}
  */
export const getFriendList = (data) => {
  return request({
    url: "/im/api/friend_role/get-friend-role",
    method: "GET",
    params: data
  });
}
/**
  * 新增好友关系组列表
  * @param data
  * @returns {*}
  */
export const addFriendList = (data) => {
  return request({
    url: "/im/api/friend_role/add-friend-role",
    method: "POST",
    data
  });
}
/**
  * 编辑好友关系组列表
  * @param data
  * @returns {*}
  */
export const editFriendList = (data) => {
  return request({
    url: "/im/api/friend_role/update-friend-role",
    method: "PUT",
    data
  });
}
/**
  * 删除好友关系组列表
  * @param data
  * @returns {*}
  */
export const delFriendList = (data) => {
  return request({
    url: "/im/api/friend_role/delete-friend-role",
    method: "DELETE",
    params: data
  });
}