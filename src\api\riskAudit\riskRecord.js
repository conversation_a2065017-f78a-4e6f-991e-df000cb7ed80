/**
 * 风险记录相关接口
 */
import request from '@/utils/request';
const prefix = 'im';

export default {
  /**
   * 风险设置列表
   * @param data
   * @returns {*}
   */
  recordsList(data) {
    return request({
      url: prefix + '/risk/record',
      method: 'get',
      params: data
    });
  },

  /**
   * 查询聊天记录
   */
  getChatRecord(data) {
    return request({
      url: prefix + '/risk/record/chatRecord',
      method: 'get',
      params: data
    });
  },

  /**
   * 获取IM用户
   */
  getIMUser(data) {
    return request({
      url: prefix + '/api/user/getUserByName',
      method: 'get',
      params: data
    });
  }
};
