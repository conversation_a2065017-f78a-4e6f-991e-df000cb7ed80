<template>
  <div>
    <!-- 抽屉组件，用于查看敏感词表 -->
    <el-drawer :before-close="handleDrawerClose" :visible.sync="drawerVisible" :with-header="false" direction="rtl" size="40%">
      <div class="drawer-container">
        <div class="header">
          <h2>{{ importType === 'sensitive' ? '查看敏感词表' : '查看过滤词表' }}</h2>
          <i class="el-icon-close" @click="handleDrawerClose" />
        </div>
        <div class="rule-form">
          <div class="header-content">
            <!-- 显示词表名称 -->
            <h4>{{ formData.name }}</h4>
          </div>
          <!-- 词条表格，支持触底加载 -->
          <div class="infinite-list">
            <el-table :data="formData.words" :header-cell-style="getRowClass" style="width: 100%">
              <el-table-column align="center" label="序号" type="index" width="80" />
              <el-table-column label="词条" prop="word">
                <template v-slot="{ row }">
                  <div class="word-cell">
                    <span class="word-text">{{ row.name || '无词条' }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作" width="100">
                <template>
                  <span>-</span>
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-row align="middle" class="pagination-container" justify="left" type="flex">
              <el-pagination
                :current-page="pagination.currentPage"
                :page-size="pagination.pageSize"
                :page-sizes="[10, 20, 30, 40, 50]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </el-row>
            <!--            <div v-if="isLoading" style="text-align: center; padding: 10px">加载中...</div>-->
            <!--            <div v-if="!isLoading && hasNoMore" style="text-align: center; padding: 10px">没有更多数据了~</div>-->
          </div>
          <div class="form-actions">
            <el-button type="primary" @click="handleDrawerClose">关闭</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  export default {
    name: 'ViewWordList',
    props: {
      currentWordList: {
        type: Object,
        default: () => ({})
      },
      tablePagination: {
        type: Object,
        default: () => ({})
      },
      visible: {
        type: Boolean,
        default: false
      },
      importType: {
        type: String,
        default: 'sensitive'
      }
    },

    /**
     * @description 组件的响应式数据
     */
    data() {
      return {
        /** @type {Object} 词表数据，包含名称和词条列表 */
        formData: {
          name: '',
          words: []
        },
        /** @type {Boolean} 是否正在加载数据 */
        isLoading: false,
        /** @type {Object} 表格分页信息，同步父组件的 tablePagination */
        tableFromPagination: { currentPage: 1, pageSize: 10, total: 0 },
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        }
      };
    },

    /**
     * @description 计算属性
     */
    computed: {
      /**
       * @description 控制抽屉的显示状态，双向绑定父组件的 visible
       * @returns {Boolean}
       */
      drawerVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      },
      /**
       * @description 是否禁用无限滚动，基于加载状态或是否还有更多数据
       * @returns {Boolean}
       */
      scrollDisabled() {
        return this.isLoading || this.hasNoMore;
      },
      /**
       * @description 是否已加载所有数据，基于当前页和总页数
       * @returns {Boolean}
       */
      hasNoMore() {
        return this.tableFromPagination.currentPage >= Math.ceil(this.tableFromPagination.total / this.tableFromPagination.pageSize);
      }
    },

    /**
     * @description 监听器
     */
    watch: {
      /**
       * @description 监听 currentWordList，同步词表名称和词条数据
       */
      currentWordList: {
        immediate: true,
        deep: true,
        handler(newVal) {
          if (newVal && Object.keys(newVal).length) {
            this.formData = {
              name: newVal.sensitiveWordName || newVal.filterWordName || '',
              words: Array.isArray(newVal.words) ? newVal.words : []
            };
          } else {
            this.formData = { name: '', words: [] };
          }
        }
      },
      /**
       * @description 监听 visible，关闭抽屉时重置表单和状态
       */
      visible(newVal) {
        if (!newVal) {
          this.resetForm();
          this.isLoading = false;
          this.tableFromPagination = { currentPage: 1, pageSize: 10, total: 0 };
        }
      },
      /**
       * @description 监听 tablePagination，同步分页信息
       */
      tablePagination: {
        deep: true,
        handler(newVal) {
          this.tableFromPagination = { ...newVal };
        }
      }
    },

    /**
     * @description 组件方法
     */
    methods: {
      setPageData(data) {
        Object.assign(this.pagination, data);
      },
      handleSizeChange(val) {
        console.log('查看 一页数量🚀🥶💩~ val', val);

        // console.log('🚀🥶💩~ val', val);
        this.$emit('size-change-view', val);
      },
      handleCurrentChange(val) {
        this.$emit('current-change-view', val);
      },
      /**
       * @description 设置表格头部样式
       * @param {Object} params - 包含 rowIndex
       * @returns {String} 样式字符串
       */
      getRowClass({ rowIndex }) {
        return rowIndex === 0 ? 'background:#f5f7fa' : '';
      },

      /**
       * @description 关闭抽屉，触发父组件的 close 事件
       */
      handleDrawerClose() {
        this.$emit('close');
      },

      /**
       * @description 重置表单数据和分页状态
       */
      resetForm() {
        this.formData = { name: '', words: [] };
        this.tableFromPagination = { currentPage: 1, pageSize: 10, total: 0 };
      },

      /**
       * @description 无限滚动加载更多词条，触发父组件的 load-more 事件
       */
      loadMore() {
        console.log('🚀🥶💩~ 123123', 123123);
        if (this.isLoading || this.hasNoMore) {
          console.log('loadMore 跳过: isLoading=', this.isLoading, 'hasNoMore=', this.hasNoMore);
          return;
        }
        console.log('触发 loadMore');
        this.isLoading = true;
        this.$emit('load-more', () => {
          console.log('loadMore 回调执行');
          this.isLoading = false;
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  /** @description 抽屉容器样式，设置内边距和全屏高度 */
  .drawer-container {
    padding: 20px;
    height: 100vh;
  }

  /** @description 头部样式，标题和关闭按钮的布局 */
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /** @description 头部标题样式 */
  .header h2 {
    font-size: 18px;
    color: #333;
  }

  /** @description 关闭图标样式 */
  .el-icon-close {
    font-size: 24px;
    cursor: pointer;
  }

  /** @description 表单容器样式，设置背景、边距和布局 */
  .rule-form {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;

    /** @description 头部内容区域，显示词表名称 */
    .header-content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      line-height: 40px;
    }

    /** @description 无限滚动列表区域，设置自适应高度和滚动 */
    .infinite-list {
      flex: 1;
      overflow-y: auto;
    }
  }

  /** @description 表单操作按钮区域，右对齐 */
  .form-actions {
    text-align: right;
    margin-top: 20px;
  }

  /** @description 表格样式，设置全宽和底部外边距 */
  .el-table {
    width: 100%;
    margin-bottom: 20px;
  }

  /** @description 表格单元格内边距 */
  .el-table th,
  .el-table td {
    padding: 8px 0;
  }

  /** @description 词条单元格，包含词条文本 */
  .word-cell {
    display: flex;
    align-items: center;
    width: 100%;
  }

  /** @description 词条文本样式，设置溢出省略 */
  .word-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
