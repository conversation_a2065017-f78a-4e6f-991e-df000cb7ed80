<template>
  <div class="app-container">
    <!-- 搜索表单区域 -->
    <div>
      <el-form ref="searchForm" :model="searchForm" inline label-width="90px">
        <el-form-item label="发送时间：" prop="timeRange">
          <el-date-picker
            v-model="searchForm.timeRange"
            :disabled-date="limitRangeDate"
            end-placeholder="结束时间"
            range-separator="至"
            size="small"
            start-placeholder="开始时间"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="handleRangeChange"
          />
        </el-form-item>
        <el-form-item label="风险内容：" prop="riskContent">
          <el-input v-model.trim="searchForm.riskContent" clearable placeholder="请输入风险会话内容搜索" size="small" />
        </el-form-item>
        <el-form-item label="发送人：" prop="sender">
          <el-select
            v-model="searchForm.sender"
            v-el-select-loadmore="handleLoadmore"
            :loading="loading"
            :remote-method="(query) => remoteMethod(query)"
            clearable
            filterable
            placeholder="请输入发送人全称搜索"
            remote
            reserve-keyword
            @clear="handleClear"
            @keyup.enter.native="getImUser"
          >
            <el-option v-for="item in userList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" size="small" type="primary" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh-left" size="small" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div v-loading="loading" class="table-container">
      <div v-show="!allData.length" class="no-more">
        <el-image src="https://document.dxznjy.com/automation/1728442200000" style="width: 100px; height: 100px" />
        <div style="color: #999; margin-top: 20px">暂无数据</div>
      </div>
      <el-table v-show="allData.length" :data="allData" :header-cell-style="getRowClass" fit size="large" style="width: 100%">
        <el-table-column align="center" header-align="center" label="序号" type="index" width="100" />
        <el-table-column header-align="left" label="风险内容">
          <template v-slot="scope">
            <el-button style="color: #ff4d4f" type="text" @click="handleRowClick(scope.row)">{{ scope.row.riskContent }}</el-button>
          </template>
        </el-table-column>
        <el-table-column header-align="left" label="收发人员">
          <template v-slot="scope">
            <div class="user-container">
              发送：
              <el-avatar :size="24" :src="scope.row.senderUrl" />
              <span class="user-info">
                <el-tag circle type="primary">{{ scope.row.senderTypeName }}</el-tag>
                {{ scope.row.senderName }}
                <el-tag v-if="scope.row.senderRoleName">{{ scope.row.senderRoleName }}</el-tag>
              </span>
            </div>
            <div class="user-container">
              接收：
              <el-avatar :size="24" :src="scope.row.receiverUrl" />
              <span class="user-info">
                <el-tag v-if="scope.row.receiverType" circle type="primary">{{ scope.row.receiverType === '1' ? '成员' : '群' }}</el-tag>

                {{ scope.row.receiverName }}
                <el-tag v-if="scope.row.receiverTypeName">{{ scope.row.receiverTypeName }}</el-tag>
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column header-align="left" label="发送时间" prop="messageTime" />
        <el-table-column header-align="left" label="应用" prop="appName" />
      </el-table>
    </div>

    <!-- 分页器 -->
    <el-row align="middle" class="pagination-container" justify="left" type="flex">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>
    <!-- 记录详情抽屉 -->
    <DrawContentCenter ref="drawContentRisk" style="height: 100%" />
  </div>
</template>

<script>
  import ChatHistory from '@/components/ChatHistory/index.vue';
  import riskAuditApi from '@/api/riskAudit/riskRecord';
  import DrawContentCenter from './components/DrawContentCenter.vue';

  export default {
    name: 'RiskRecording',
    components: { DrawContentCenter, ChatHistory },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        oldName: '',
        searchForm: {
          timeRange: [],
          riskContent: undefined,
          sender: undefined,
          pageNumber: 1,
          pageSize: 10
        },
        allData: [],
        loading: false,
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        drawerVisible: false,
        direction: 'rtl',
        chooseType: '1',
        currentRecord: {},
        messageList: [],
        userList: [],
        pickerOptions: {
          disabledDate: (time) => {
            // 计算6个月前的日期
            const sixMonthsAgo = new Date();
            sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

            // 限制选择范围：6个月内
            return time.getTime() > Date.now() || time.getTime() < sixMonthsAgo.getTime();
          }
        },
        dateRange: null, // 日期区间：[start, end]
        searchName: '',
        selectObj: {
          pageNum: 1,
          pageSize: 10
        },
        optionTotal: 0
      };
    },
    created() {
      this.loadData();
    },
    watch: {
      searchName: {
        handler(oldVal, newVal) {
          if (newVal !== oldVal) {
            this.userList = [];
            this.selectObj.pageNum = 1;
          }
        },
        immediate: true
      }
    },

    methods: {
      handleClear() {
        this.userList = [];
      },
      remoteMethod(value) {
        this.searchName = value;
      },
      /**
       * 获取IM用户
       * @returns {Promise<void>}
       */
      async getImUser() {
        try {
          if (!this.searchName) return;
          const { code, data } = await riskAuditApi.getIMUser({ name: this.searchName, pageNum: this.selectObj.pageNum, pageSize: 10 });
          if (code === 20000) {
            this.userList = this.userList.concat(
              data.data.map((item) => {
                return {
                  value: item.tencentId,
                  label: item.nickname
                };
              }) || []
            );

            this.optionTotal = Number(data.totalPage);
          }
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
        }
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loading) {
          if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          this.selectObj.pageNum++;
          this.getImUser();
        }
      },
      // 校验范围是否超出 6 个月
      handleRangeChange(val) {
        if (val && val.length === 2) {
          const [start, end] = val;
          const startDate = new Date(start);
          const endDate = new Date(end);

          const yearDiff = endDate.getFullYear() - startDate.getFullYear();
          const monthDiff = endDate.getMonth() - startDate.getMonth();
          const totalMonths = yearDiff * 12 + monthDiff;

          if (totalMonths > 6 || (totalMonths === 6 && endDate.getDate() > startDate.getDate())) {
            this.$message.error('选择时间范围不能超过6个月');
            this.searchForm.timeRange = null;
          }
        }
      },

      limitRangeDate(time) {
        const range = this.dateRange;

        if (!range || range.length !== 2 || !range[0] || !range[1]) {
          // 用户还没选完整范围时，先只判断单边限制
          if (range && range[0]) {
            const start = new Date(range[0]);
            const min = new Date(start);
            min.setMonth(min.getMonth() - 6);

            const max = new Date(start);
            max.setMonth(max.getMonth() + 6);

            return time < min || time > max;
          }
          return false; // 初始状态不限制
        }

        // 已经选完区间，直接不限制（防止回显报错）
        return false;
      },

      async loadData() {
        this.loading = true;
        try {
          const params = { ...this.searchForm };
          if (params.timeRange && params.timeRange.length === 2) {
            params.startDate = params.timeRange[0];
            params.endDate = params.timeRange[1];
          }
          delete params.timeRange;
          params.pageNumber = this.pagination.currentPage;
          params.pageSize = this.pagination.pageSize;
          const res = await riskAuditApi.recordsList(params);
          this.allData = res.data.data || [];
          this.pagination.total = res.data.total || 0;
        } catch (error) {
          console.error('数据加载失败', error);
          this.allData = [];
          this.$message.error('数据加载失败');
        } finally {
          this.loading = false;
        }
      },

      async handleRowClick(row) {
        this.$refs.drawContentRisk.drawer = true;
        this.$refs.drawContentRisk.isViewChat = true;
        await this.$refs.drawContentRisk.getMessage(row, row.messageId);
      },

      getRowClass({ rowIndex }) {
        return rowIndex === 0 ? 'background:#f5f7fa' : '';
      },

      handleSearch() {
        this.pagination.currentPage = 1;
        this.loadData();
      },

      handleReset() {
        this.$refs.searchForm.resetFields();
        this.pagination.currentPage = 1;
        this.searchForm.pageNumber = 1;
        this.searchForm.pageSize = 10;
        this.pagination.pageSize = 10;
        this.userList = [];
        this.loadData();
      },

      /**
       * 页面数调整
       * @param val
       */
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.pagination.currentPage = 1;
        this.searchForm.pageNumber = 1;
        this.loadData();
      },

      //页码改变
      handleCurrentChange(val) {
        this.pagination.currentPage = val;
        this.searchForm.pageNumber = val;
        this.loadData();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 20px;
  }

  .table-container {
    background-color: #fff;
  }

  .no-more {
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .el-table {
    border: 1px solid #ebeef5;
  }

  .el-table th,
  .el-table td {
    border-right: none;
  }

  .pagination-container {
    padding: 20px 0;
    background-color: #fff;
  }

  .user-container {
    display: flex;
    align-items: center;
    height: 30px;
  }

  .user-info {
    margin-left: 8px;
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
  }

  .drawer-content {
    display: flex;
    height: 100%;
  }

  .chat-section {
    width: 60%;
    padding: 10px;
  }

  .chat-header {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
  }

  .info-section {
    width: 40%;
    padding: 20px;
  }

  .description-form {
    .el-form-item {
      margin-bottom: 15px;
    }

    .el-form-item__label {
      font-weight: bold;
      color: #606266;
    }

    .el-form-item__content {
      color: #909399;
    }
  }

  .el-divider--vertical {
    height: 100%;
  }

  .el-divider--horizontal {
    margin: 0;
  }
</style>
