<template>
  <div class="app-container">
    <!-- 搜索表单区域 -->
    <el-form ref="searchForm" :model="searchForm" label-width="90px" inline>
      <el-form-item label="规则名称：" prop="ruleName">
        <el-input v-model.trim="searchForm.ruleName" size="small" placeholder="请输入规则名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleSearch">查询</el-button>
        <el-button size="small" icon="el-icon-refresh-left" @click="handleReset">重置</el-button>
        <el-button size="small" type="primary" @click="manageSensitiveWords">管理敏感词表</el-button>
        <el-button size="small" type="success" @click="createNewRule">新建规则</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <div v-loading="loading">
      <div v-show="!currentPageData.length" class="nomore">
        <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000" />
        <div style="color: #999; margin-top: 20px">暂无数据</div>
      </div>
      <el-table v-show="currentPageData.length" :data="currentPageData" style="width: 100%" :header-cell-style="getRowClass" fit size="large">
        <el-table-column type="index" label="序号" width="100" header-align="center" align="center" />
        <el-table-column label="规则名称" header-align="left">
          <template v-slot="scope">
            <el-tooltip :content="scope.row.ruleName" placement="top">
              <span @click="editRule(scope.row, false)" class="rule-name">
                {{ scope.row.ruleName }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="ruleObjectValue" label="监控对象" header-align="left">
          <template v-slot="scope">
            <span v-if="scope.row.ruleObjectValue">{{ scope.row.ruleObjectValue }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="sensitiveWordName" label="监控词表" header-align="left" width="300">
          <template v-slot="scope">
            <el-tooltip class="item" effect="dark" :content="scope.row.sensitiveWordName" placement="top" v-if="scope.row.sensitiveWordName">
              <span class="ellipsis-text">{{ scope.row.sensitiveWordName }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="numberOfHits" label="命中数" header-align="left" />
        <el-table-column prop="createTime" label="新增时间" header-align="left" />
        <el-table-column label="操作" header-align="left" width="150">
          <template v-slot="scope">
            <el-button type="text" @click.stop="editRule(scope.row, true)">编辑</el-button>
            <el-button type="text" style="color: #ff4d4f" @click.stop="deleteRule(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页器 -->
    <el-row type="flex" justify="left" align="middle" class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>

    <DrawContent
      ref="drawContent"
      :roleList="roleList"
      :sensitiveList="sensitiveList"
      :filterList="filterList"
      :monitorObject="monitorObject"
      :current-rule="currentRule"
      :is-editing="isEditing"
      :is-creating="isCreating"
      @saveRule="saveRule"
      @close="handleClose"
    />
  </div>
</template>

<script>
  import DrawContent from '@/views/riskAudit/complaintManage/components/DrawContent.vue';
  import settingApi from '@/api/riskAudit/riskSetting';

  export default {
    name: 'RiskSetting',
    components: { DrawContent },
    data() {
      return {
        searchForm: { ruleName: '', pageSize: 10, pageNumber: 1 },
        allData: [],
        loading: false,
        pagination: { currentPage: 1, pageSize: 10, total: 0 },
        currentRule: {},
        isEditing: false,
        isCreating: false,
        roleList: [],
        sensitiveList: [],
        filterList: [],
        monitorObject: [],
        currentPageData: [],
        isSubmitLoading: false
      };
    },

    created() {
      this.loadData();
      this.getFilteredData();
      this.sensitiveData();
      this.getMonitorObject();
      this.getRoles();
    },
    methods: {
      /**
       * 获取监控对象列表数据
       */
      async getMonitorObject() {
        try {
          const { code, data } = await settingApi.getMonitorObject();
          if (code === 20000) {
            this.monitorObject = data || [];
          }
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
          this.monitorObject = [];
        }
      },
      /**
       * 获取过滤词列表数据
       * @returns {Promise<void>}
       */
      async getFilteredData() {
        try {
          const { code, data } = await settingApi.getFilterWordsApi();
          if (code === 20000) {
            this.filterList =
              data.data.map((item) => {
                return {
                  label: item.filterWordName,
                  value: item.filterWordCode
                };
              }) || [];
          }
        } catch (error) {
          this.filterList = [];
          console.log('🚀🥶💩~ error', error);
        }
      },

      /**
       * 获取敏感词列表数据
       * @returns {Promise<void>}
       */
      async sensitiveData() {
        try {
          const { code, data } = await settingApi.sensitiveWordsListApi();
          if (code === 20000) {
            this.sensitiveList =
              data.data.map((item) => {
                return {
                  label: item.sensitiveWordName,
                  value: item.sensitiveWordCode
                };
              }) || [];
          }
        } catch (error) {
          this.sensitiveList = [];
          console.log('🚀🥶💩~ error', error);
        }
      },

      /**
       * 查询业务角色列表
       * @returns {Promise<void>}
       */
      async getRoles() {
        try {
          const { code, data } = await settingApi.getRoles({
            pageNumber: 0,
            pageSize: 99999
          });
          if (code === 20000) {
            this.roleList =
              data.map((item) => {
                return {
                  label: item.roleName,
                  value: item.roleValue
                };
              }) || [];
          }
        } catch (error) {}
      },

      getRowClass({ rowIndex }) {
        return rowIndex === 0 ? 'background:#f5f7fa' : '';
      },

      async loadData() {
        const that = this;
        that.loading = true;
        try {
          const { code, data } = await settingApi.settingList(that.searchForm);
          if (code === 20000) {
            that.currentPageData = data.data || [];
            this.pagination.total = parseInt(data.totalCount);
          }
        } catch (error) {
          console.error('数据加载失败', error);
          this.$message.error('数据加载失败');
        } finally {
          this.loading = false;
        }
      },
      handleSearch() {
        this.pagination.currentPage = 1;
        this.loadData();
      },
      handleReset() {
        this.$refs.searchForm.resetFields();
        this.searchForm.pageNumber = 1;
        this.searchForm.pageSize = 10;
        this.pagination.currentPage = 1;
        this.pagination.pageSize = 10;
        this.loadData();
      },
      handleSizeChange(val) {
        this.searchForm.pageSize = val;
        this.searchForm.pageNumber = 1;
        this.pagination.pageSize = val;
        this.pagination.currentPage = 1;
        this.loadData();
      },
      handleCurrentChange(val) {
        this.pagination.currentPage = val;
        this.searchForm.pageNumber = val;
        this.loadData();
      },
      manageSensitiveWords() {
        this.$router.push('/riskAudit/sensitiveWordsList');
      },
      createNewRule() {
        this.currentRule = {
          ruleName: '',
          ruleCode: '',
          sensitiveWords: '',
          strategies: [],
          specialConditions: [],
          riskStrategyCos: [{ notifyMethod: '仅做记录' }]
        };
        this.isCreating = true;
        this.isEditing = false;
        this.$refs.drawContent.drawer = true;
      },

      async editRule(row, isEdit) {
        this.isEditing = isEdit;
        this.isCreating = false;
        try {
          const { code, data } = await settingApi.getSettingDetail(row.id);
          if (code === 20000) {
            this.currentRule = data.data;
            this.currentRule.ruleObject = data.data.ruleObject.split('、') || [];
            this.$refs.drawContent.drawer = true;
            this.$refs.drawContent.isSubmitLoading = false;
          }
        } catch (error) {
          console.error('🚀🥶💩~ error', error);
          this.this.currentRule = {
            ruleName: '',
            scope: '',
            ruleObject: [],
            mobileIsRecognized: 0,
            riskStrategyCos: [],
            id: row.id
          };
        }
      },

      /**
       * 调用新增规则
       */
      async addRule(updatedRule) {
        try {
          const { code, data, message } = await settingApi.addSetting(updatedRule);
          if (code === 20000) {
            this.currentRule = data;
            this.isCreating = false;
            this.isEditing = false;
            this.$refs.drawContent.isSubmitLoading = false;
            this.$message.success(message);
          }
        } catch (error) {
          console.log('🚀🥶💩~ error', error);
          this.$message.error('规则创建失败');
        }
      },

      /**
       * 保存规则
       * @param updatedRule
       * @returns {Promise<void>}
       */
      async saveRule(updatedRule) {
        await this.addRule(updatedRule);
        this.handleClose();
        await this.loadData();
      },
      /**
       * 删除规则
       * @param id
       */
      deleteRule(id) {
        this.$confirm('是否确认删除规则？', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
          .then(async () => {
            const { code } = await settingApi.deleteSetting(id);
            if (code === 20000) {
              await this.loadData();
              this.$message.success('删除成功!');
            }
          })
          .catch(() => this.$message.info('已取消删除'));
      },

      handleClose() {
        this.$refs.drawContent.drawer = false;
        this.isEditing = false;
        this.isCreating = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .nomore {
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .el-table {
    border: 1px solid #ebeef5;
  }

  .el-table th,
  .el-table td {
    border-right: none;
  }

  .pagination-container {
    padding: 20px 0;
    background-color: #fff;
  }

  .rule-name {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    color: #409eff;
  }

  .rule-name:hover {
    text-decoration: underline;
  }

  .ellipsis-text {
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
</style>
