/**
 * “权限管理”相关接口
 */
import request from '@/utils/request';

const prefix = 'im';

export default {
  /**
   * 添加权限
   * @param data
   */
  addPerm(data) {
    return request({
      url: prefix + '/api/sys-perm',
      method: 'post',
      data
    });
  },

  /**
   * 同步菜单权限数据
   * @param data
   */
  syncMenuPerms(permArr) {
    return request({
      // url: '/train/sys/perm/sync/menu',
      url: prefix + '/api/sys-perm/sync-menu',
      method: 'put',
      data: permArr
    });
  },
  /**
   * 同步接口权限数据
   * @param data
   */
  syncApiPerms(permArr) {
    return request({
      url: '/train/sys/perm/sync/api',
      method: 'post',
      data: permArr
    });
  },

  /**
   * 删除权限
   * @param data
   */
  deletePerm(id) {
    return request({
      url: prefix + `/api/sys-perm/${id}`,
      method: 'delete'
    });
  },

  /**
   * 更新权限
   * @param data
   */
  updatePerm(data) {
    return request({
      url: prefix + '/api/sys-perm/info',
      method: 'patch',
      data
    });
  },

  /**
   * 查询接口权限元数据
   * @param perm
   */
  listApiPermMetadata() {
    return request({
      url: '/train/sys/perm/meta/api',
      method: 'get'
    });
  },

  /**
   * 列出所有菜单、按钮、接口等权限
   * @param perm
   */
  listAllPermissions() {
    return request({
      url: prefix + '/api/sys-perm/list-all',
      method: 'get'
    });
  },

  /**
   * 列出按钮权限，按parent字段分组
   * @param perm
   */
  listBtnPermGroupByParent() {
    return request({
      url: prefix + '/api/sys-perm/btn-map',
      method: 'get'
    });
  },

  /**
   * 根据后台角色id查询业务数据权限
   * @param perm
   */
  listPermissions(data) {
    return request({
      url: prefix + '/api/data-perm/get-perm',
      method: 'get',
      params: data
    });
  },
  /**
   * 保存后台角色数据权限
   * @param perm
   */
  savePermissions(data) {
    return request({
      url: prefix + '/api/data-perm/save-data',
      method: 'post',
      data
    });
  }
};
